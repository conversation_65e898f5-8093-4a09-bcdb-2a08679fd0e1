<?php
/* Template Name: Members list - landing page */
get_header();
?>

<main role="main" id="content" class="content content-archive clearfix sb-members">
    <?php while (have_posts()): the_post(); ?>
        <?php get_template_part('page-header'); ?>

        <section class="archive-articles loop">
            <?php
            $membersQuery =  new \WP_Query([
                'post_type' => 'medlemmar',
                'post_status' => 'publish',
                'posts_per_page' => -1,
                'orderby' => [
                    'title' => 'ASC',
                ],
            ]);


            if ($membersQuery->have_posts() && class_exists("ACF")) {

                echo "<div class='sb-members__terms'>";
                    $members_terms = get_terms([
                        'taxonomy' => 'user_categories',
                        'hide_empty' => false,
                    ]);
                    $children = [];
                    echo "<div class='all'>";
                        echo "<a href='#' class='active' data-filter='all'>".__("All", 'swedenbio')."</a>";
                    echo "</div>";
                    echo "<div class='parents'>";
                        foreach ($members_terms as $mTerm) {
                            if ($mTerm->parent === 0) {
                                echo "<a href='#' data-filter='".$mTerm->slug."' data-id='".$mTerm->term_id."'>".$mTerm->name."</a>";
                            } else {
                                $children[$mTerm->parent][] = $mTerm;
                            }
                        }
                    echo "</div>";
                    if (!empty($children)) {
                       // echo "<div class='children' data-parent='$key'>";
                        foreach ($children as $key => $child) {
                            echo "<div class='children' data-parent='$key'>";
                                foreach ($child as $cTerm) {
                                    echo "<a href='#' data-parent='$key' data-filter='".$cTerm->slug."' data-id='".$cTerm->term_id."'>".$cTerm->name."</a>";
                                }
                            echo "</div>";
                        }
                       // echo "</div>";
                    }
                echo "</div>";

                echo "<div class='sb-members__search'>";
                    echo "<div class='sb-members__search--content'>";
                        echo "<input type='text' class='sb-members__search--input' placeholder='".__('Search among members', 'swedenbio')."'>";
                        echo '<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="24" height="24" viewBox="0 0 24 24"><path  d="M15.5,14h-.79l-.28-.27a6.51,6.51,0,1,0-.7.7l.27.28v.79l5,4.99L20.49,19Zm-6,0A4.5,4.5,0,1,1,14,9.5,4.494,4.494,0,0,1,9.5,14Z"/><path fill="none" d="M0,0H24V24H0Z"/></svg>';
                    echo "</div>";
                echo "</div>";

                echo "<h2 class='sb_styled-title'>";
                    echo __("All", 'swedenbio');
                echo "</h2>";

                echo "<div class='sb-members__list'>";
                while ($membersQuery->have_posts()) {
                    $membersQuery->the_post();
                    $website = get_field("website");
                    $postId = get_the_ID();
                    $terms = get_the_terms($postId, "user_categories");
                    $terms_to_class = '';
                    $termsNames = [];
                    if ($terms) {
                        foreach ($terms as $term) {
                            $terms_to_class .= ' '.$term->slug.' ';
                            if ($term->parent) {
                                $termsNames[] = $term->name;
                            }
                        }
                    }


                        echo "<div class='sb-members__list--member $terms_to_class' data-title='".strtolower(get_the_title())."'>";
                            echo "<div class='sb-members__list--member__content'>";
                                echo "<h2 class='lists-title'><a href='".get_permalink()."'> ".get_the_title()."</a></h2>";
                                echo "<a href='".$website."' class='website' target='_blank'>".$website."</a>";
                            echo "</div>";
                            echo "<div class='sb-members__list--member__categories'>";
                                echo implode(', ', $termsNames);
                            echo "</div>";
                        echo "</div>";

                }
                echo "</div>";
            }

            wp_reset_postdata();
            ?>

        </section>
        <section class="article-content">
            <article <?php post_class('article'); ?>>
                <?php
                the_content('');
                ?>
            </article>
        </section>
    <?php endwhile; ?>


</main>

<?php get_footer();