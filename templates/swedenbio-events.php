<?php
/* Template Name: Events list - landing page */
get_header();
?>

<main role="main" id="content" class="content content-archive clearfix swedenbio-events-list">
    <?php while (have_posts()): the_post(); ?>
        <?php get_template_part('page-header'); ?>

        <section class="archive-articles loop">
            <?php
            $page = get_query_var('paged') ?  get_query_var('paged'): 1;
            $calendarQuery =  new \WP_Query([
                'post_type' => 'sb_events',
                'post_status' => 'publish',
                'posts_per_page' => get_option( 'posts_per_page' ),
                'paged' => $page,
                'meta_key'   => 'sb-event_start_date',
                'orderby'    => 'meta_value_num',
                'order'      => 'ASC',
                'meta_query' => [
                    'relation' => 'AND',
                    [
                        'relation' => 'OR',
                        'start_date' => [
                            'key' => 'sb-event_start_date',
                            'value' => date('Ymd'),
                            'compare' => '>='
                        ],
                        'end_date' => [
                            'key' => 'sb-event_end_date',
                            'value' => date('Ymd'),
                            'compare' => '>='
                        ],
                    ],
                    [
                            'start_time' => [
                            'key' => 'sb-event_start_time',
                            'compare' => 'EXISTS',
                        ],
                    ]
                ],
//                'orderby' => [
//                    'start_date' => 'ASC',
//                    'start_time' => 'ASC',
//                    'end_date' => 'ASC'
//                ],
            ]);

            $months = [
                'januari',
                'februari',
                'mars',
                'april',
                'maj',
                'juni',
                'juli',
                'augusti',
                'september',
                'oktober',
                'november',
                'december',
            ];

            $currentYear = false;
            if ($calendarQuery->have_posts() && class_exists("ACF")) {
                while ($calendarQuery->have_posts()) {
                    $calendarQuery->the_post();
                    $startDate = get_field("sb-event_start_date");
                    $endDate = get_field("sb-event_end_date");
                    $place = get_field("sb-event_place");
                    $type = get_field("sb-event_event_type");
                    $memberDiscount = get_field("sb-event_discount");
                    if (!$startDate) {
                        continue;
                    }
                    $date = new \DateTime($startDate);
                    if ($date->format('Y') !== $currentYear) {
                        $currentYear = $date->format('Y');
                        echo "<span class='sb-events-list__year'>$currentYear</span>";
                    }
                    //echo "<div class='sb-events-list__events'>";
                        echo "<div class='sb-events-list__event'>";
                            echo "<div class='sb-events-list__event--date'>";
                                echo "<span class='date-month'>";
                                    echo $months[$date->format('n') - 1];
                                echo "</span>";
                                echo "<span class='date-day'>";
                                    echo $date->format('d');
                                echo "</span>";
                                if ($endDate && $startDate != $endDate) {
                                    $edate = new \DateTime($endDate);
                                    echo "-";
                                    echo "<span class='date-month'>";
                                        echo $months[$edate->format('n') - 1];
                                    echo "</span>";
                                    echo "<span class='date-day'>";
                                        echo $edate->format('d');
                                    echo "</span>";
                                }
                            echo "</div>";
                            echo "<div class='sb-events-list__event--content'>";
                                
                                echo "<h2 class='lists-title'><a href='".get_permalink()."'>".get_the_title()."</a></h2>";
                                
                                if ($place) {
                                    echo "<span class='event-place'>$place</span>";
                                }

                                if ($memberDiscount) {
                                    echo "<div class='sb-events-list__event--buttons' style='text-align: left'>";
                                    echo '<span class="btn small white-alt">';
                                    echo __('Member discount', 'swedenbio');
                                    echo '</span>';
                                    echo '</div>';
                                }

                            echo "</div>";
                            echo "<div class='sb-events-list__event--buttons'>";
                                echo "<a href='".get_permalink()."' title='".__('Read more', 'swedenbio')."' class='btn small white'>".__('Read more', 'swedenbio')."</a>";
                                if ($type) {
                                    if ($type === "own") {
                                        echo "<span class='btn small event-type-$type'>";
                                        echo __('SwedenBio event', 'swedenbio');
                                        echo "</span>";
                                    } else if ($type === 'partner') {
                                        echo "<span class='btn small event-type-$type blue'>";
                                        echo __('Partner event', 'swedenbio');
                                        echo "</span>";
                                    } else if ($type === 'external') {
                                        echo "<span class='btn small event-type-$type yellow'>";
                                        echo __('External event', 'swedenbio');
                                        echo "</span>";
                                    }
                                }
                            echo "</div>";
                        echo "</div>";
                    //echo "</div>";
                }
            }
            wp_reset_postdata();
            ?>
            <?php

            $temp_query = $wp_query;
            $wp_query = NULL;
            $wp_query = $calendarQuery;

            get_template_part('pagination');

            $wp_query = NULL;
            $wp_query = $temp_query;

            ?>
        </section>
        <section class="article-content">
            <article <?php post_class('article'); ?>>
                <?php
                the_content('');
                ?>
            </article>
        </section>
    <?php endwhile; ?>


</main>

<?php get_footer();