<?php
if (class_exists("acf") && function_exists("have_rows")) {
    if (have_rows('after_article_cta')) {
        while(have_rows('after_article_cta')) {
            the_row();
            $text = get_sub_field('text');
            $isExternal = get_sub_field('is_external');
            $url = '#';
            if ($isExternal) {
                $external = get_sub_field('external_url');
                if ($external) {
                    $url = $external;
                }
            } else {
                $internal = get_sub_field('internal_url');
                if ($internal) {
                    $url = get_permalink($internal);
                }
            }
            if ($text) {
                echo "<a href='$url' class='btn small' target='".($isExternal ? '_blank' : '_self')."'>";
                echo $text;
                if ($isExternal) {
                    echo "<img src='".get_stylesheet_directory_uri()."/assets/images/open_in_new.svg' alt='".__("Open in new tab", "swedenbio")."'>";
                }
                echo "</a>";
            }
        }
    }
}
?>