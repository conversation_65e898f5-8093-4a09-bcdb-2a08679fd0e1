<?php
if (!class_exists('ACF')) {
    return;
}
global $post;
$queried_object = get_queried_object();
// red-box
$rb_attach_to_posts = get_field('GLOBAL_RED_BOX-attach_to_posts', "option");
$rb_excluded_posts = get_field('GLOBAL_RED_BOX-exclude_from_posts', "option");
$rb_attach_to_archives = get_field('GLOBAL_RED_BOX-attach_to_archives', "option");
$rb_excluded_terms = get_field('GLOBAL_RED_BOX-exclude_from_categories', "option");
$rb_attach_to_calendar = get_field('GLOBAL_RED_BOX-attach_to_calendar', "option");
$rb_excluded_calendar = get_field('GLOBAL_RED_BOX-exclude_from_calendar', "option");
$rb_attach_to_members = get_field('GLOBAL_RED_BOX-attach_to_members', "option");
$rb_excluded_members = get_field('GLOBAL_RED_BOX-exclude_from_members', "option");
$rb_attach_to_members_posts = get_field('GLOBAL_RED_BOX-attach_to_members_posts', "option");
$rb_excluded_members_posts = get_field('GLOBAL_RED_BOX-exclude_from_members_posts', "option");

if (is_singular(['post']) && $rb_attach_to_posts) {
    if (!$rb_excluded_posts || (is_array($rb_excluded_posts) && !in_array($post->ID, $rb_excluded_posts))) {
        echo do_shortcode("[sb_global_red_box]");
    }
}

if (is_singular(['sb_events']) && $rb_attach_to_calendar) {
    if (!$rb_excluded_calendar || (is_array($rb_excluded_calendar) && !in_array($post->ID, $rb_excluded_calendar))) {
        echo do_shortcode("[sb_global_red_box]");
    }
}

if (is_singular(['medlemmar']) && $rb_attach_to_members) {
    if (!$rb_excluded_members || (is_array($rb_excluded_members) && !in_array($post->ID, $rb_excluded_members))) {
        echo do_shortcode("[sb_global_red_box]");
    }
}

if (is_singular(['sb_member_posts']) && $rb_attach_to_members_posts) {
    if (!$rb_excluded_members_posts || (is_array($rb_excluded_members_posts) && !in_array($post->ID, $rb_excluded_members_posts))) {
        echo do_shortcode("[sb_global_red_box]");
    }
}

if ($rb_attach_to_archives && (is_archive() || (!is_front_page() && is_home()) || is_search())) {
    if (!$rb_excluded_terms || (is_array($rb_excluded_terms) && !in_array($queried_object->term_id, $rb_excluded_terms) && $queried_object->taxonomy === 'category')) {
        echo do_shortcode("[sb_global_red_box]");
    }
}


// calendar-box
$calendar_attach_to_posts = get_field('GLOBAL_CALENDAR_BOX-attach_to_posts', "option");
$calendar_excluded_posts = get_field('GLOBAL_CALENDAR_BOX-exclude_from_posts', "option");
$calendar_attach_to_archives = get_field('GLOBAL_CALENDAR_BOX-attach_to_archives', "option");
$calendar_excluded_terms = get_field('GLOBAL_CALENDAR_BOX-exclude_from_categories', "option");
$calendar_attach_to_calendar = get_field('GLOBAL_CALENDAR_BOX-attach_to_calendar', "option");
$calendar_excluded_calendar = get_field('GLOBAL_CALENDAR_BOX-exclude_from_calendar', "option");
$calendar_attach_to_members = get_field('GLOBAL_CALENDAR_BOX-attach_to_members', "option");
$calendar_excluded_members = get_field('GLOBAL_CALENDAR_BOX-exclude_from_members', "option");
$calendar_attach_to_members_posts = get_field('GLOBAL_CALENDAR_BOX-attach_to_members_posts', "option");
$calendar_excluded_members_posts = get_field('GLOBAL_CALENDAR_BOX-exclude_from_members_posts', "option");

if (is_singular(['post']) && $calendar_attach_to_posts) {
    if (!$calendar_excluded_posts || (is_array($calendar_excluded_posts) && !in_array($post->ID, $calendar_excluded_posts))) {
        echo do_shortcode("[sb_global_calendar_box]");
    }
}

if (is_singular(['sb_events']) && $calendar_attach_to_calendar) {
    if (!$calendar_excluded_calendar || (is_array($calendar_excluded_calendar) && !in_array($post->ID, $calendar_excluded_calendar))) {
        echo do_shortcode("[sb_global_calendar_box]");
    }
}

if (is_singular(['medlemmar']) && $calendar_attach_to_members) {
    if (!$calendar_excluded_members || (is_array($calendar_excluded_members) && !in_array($post->ID, $calendar_excluded_members))) {
        echo do_shortcode("[sb_global_calendar_box]");
    }
}

if (is_singular(['sb_member_posts']) && $calendar_attach_to_members_posts) {
    if (!$calendar_excluded_members_posts || (is_array($calendar_excluded_members_posts) && !in_array($post->ID, $calendar_excluded_members_posts))) {
        echo do_shortcode("[sb_global_calendar_box]");
    }
}

if ($calendar_attach_to_archives && (is_archive() || (!is_front_page() && is_home()) || is_search())) {
    if (!$calendar_excluded_terms || (is_array($calendar_excluded_terms) && !in_array($queried_object->term_id, $calendar_excluded_terms) && $queried_object->taxonomy === 'category')) {
        echo do_shortcode("[sb_global_calendar_box]");
    }
}


// gray-box
$gray_attach_to_posts = get_field('GLOBAL_GREY_BOX-attach_to_posts', "option");
$gray_excluded_posts = get_field('GLOBAL_GREY_BOX-exclude_from_posts', "option");
$gray_attach_to_archives = get_field('GLOBAL_GREY_BOX-attach_to_archives', "option");
$gray_excluded_terms = get_field('GLOBAL_GREY_BOX-exclude_from_categories', "option");
$gray_attach_to_calendar = get_field('GLOBAL_GREY_BOX-attach_to_calendar', "option");
$gray_excluded_calendar = get_field('GLOBAL_GREY_BOX-exclude_from_calendar', "option");
$gray_attach_to_members = get_field('GLOBAL_GREY_BOX-attach_to_members', "option");
$gray_excluded_members = get_field('GLOBAL_GREY_BOX-exclude_from_members', "option");
$gray_attach_to_members_posts = get_field('GLOBAL_GREY_BOX-attach_to_members_posts', "option");
$gray_excluded_members_posts = get_field('GLOBAL_GREY_BOX-exclude_from_members_posts', "option");

if (is_singular(['post']) && $gray_attach_to_posts) {
    if (!$gray_excluded_posts || (is_array($gray_excluded_posts) && !in_array($post->ID, $gray_excluded_posts))) {
        echo do_shortcode("[sb_global_gray_box]");
    }
}

if (is_singular(['sb_events']) && $gray_attach_to_calendar) {
    if (!$gray_excluded_calendar || (is_array($gray_excluded_calendar) && !in_array($post->ID, $gray_excluded_calendar))) {
        echo do_shortcode("[sb_global_gray_box]");
    }
}

if (is_singular(['medlemmar']) && $gray_attach_to_members) {
    if (!$gray_excluded_members || (is_array($gray_excluded_members) && !in_array($post->ID, $gray_excluded_members))) {
        echo do_shortcode("[sb_global_gray_box]");
    }
}

if (is_singular(['sb_member_posts']) && $gray_attach_to_members_posts) {
    if (!$gray_excluded_members_posts || (is_array($gray_excluded_members_posts) && !in_array($post->ID, $gray_excluded_members_posts))) {
        echo do_shortcode("[sb_global_gray_box]");
    }
}

if ($gray_attach_to_archives && (is_archive() || (!is_front_page() && is_home()) || is_search())) {
    if (!$gray_excluded_terms || (is_array($gray_excluded_terms) && !in_array($queried_object->term_id, $gray_excluded_terms) && $queried_object->taxonomy === 'category')) {
        echo do_shortcode("[sb_global_gray_box]");
    }
}