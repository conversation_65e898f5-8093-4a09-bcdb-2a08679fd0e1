<h3 class="article-aside--title"><?php _e('Time and location', 'swedenbio'); ?></h3>
<?php
if (class_exists('ACF')) {
    $startDate = get_field("sb-event_start_date");
    $endDate = get_field("sb-event_end_date");
    $startTime = get_field("sb-event_start_time");
    $endTime = get_field("sb-event_end_time");
    $place = get_field("sb-event_place");
    $type = get_field("sb-event_event_type");
    $isExclusive = get_field("sb-event_exclusive");
    if ($startDate) {
        $sDate = new \DateTime($startDate);
        echo "<div class='article-aside--event-details'>";
            echo "<h5 class='sub-title'>".__("Date", 'swedenbio')."</h5>";
            echo "<div>";
                echo $sDate->format('j/n Y');
                if ($endDate) {
                    $eDate = new \DateTime($endDate);

                    if ($eDate != $sDate) {
                        echo ' - '.$eDate->format('j/n Y');
                    }
                }
            echo "</div>";
        echo "</div>";
    }

    if ($startTime) {
        echo "<div class='article-aside--event-details'>";
            echo "<h5 class='sub-title'>".__("Time", 'swedenbio')."</h5>";
            echo "<div>";
            echo $startTime;
            if ($endTime && $endTime != $startTime) {
                echo ' - '.$endTime;
            }
            echo "</div>";
        echo "</div>";
    }

    if ($place) {
        echo "<div class='article-aside--event-details'>";
            echo "<h5 class='sub-title'>".__("Location", 'swedenbio')."</h5>";
            echo "<div>";
            echo $place;
            echo "</div>";
        echo "</div>";
    }

    if ($isExclusive) {
        echo "<div class='article-aside--event-details'>";
        echo "<h5 class='sub-title'>".__("Exclusively for members", 'swedenbio')."</h5>";
        echo "</div>";
    }
}
?>