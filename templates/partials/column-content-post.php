<span class="article-aside--date"><?php echo get_the_date(); ?></span>
<?php
$terms = get_the_terms(get_the_ID(), 'category');
if ($terms) {
    echo "<div class='article-aside--terms'>";
    foreach ($terms as $term) {
        $link = get_term_link($term);
        if ($term->term_id !== 1) {
            echo "<span class='category'>";
            if ($link) {
                echo "<a href='$link'>";
            }
            echo $term->name;
            if ($link) {
                echo "</a>";
            }
            echo "</span>";
        }
    }
    echo "</div>";
}
?>