<?php
/* Template Name: Members Posts - landing page */
get_header();
?>

<main role="main" id="content" class="content content-archive clearfix sb-members_posts">
    <?php while (have_posts()): the_post(); ?>
        <?php get_template_part('page-header'); ?>

        <section class="archive-articles loop">
            <?php
            $page = get_query_var('paged') ?  get_query_var('paged'): 1;
            $queryArgs = [
                'post_type' => 'sb_member_posts',
                'posts_per_page' => get_option( 'posts_per_page' ),
                'paged' => $page,
                'post_status' => 'publish',
            ];
            $membersQuery =  new \WP_Query($queryArgs);

            if ($membersQuery->have_posts()) {
                while ($membersQuery->have_posts()) {
                    $membersQuery->the_post();
                    $postId = get_the_ID();
                    $image = get_the_post_thumbnail($postId, 'uncropped_list');
                    $authorID = get_the_author_meta("ID");
                    $user_meta = get_userdata($authorID);
					$companyName = get_field('sb_company-name');
                    ?>
                    <article <?php post_class(['loop-article']); ?>>
                        <?php


                        echo "<div class='loop-article__image'>";
                            echo $image;
                        echo "</div>";

                        ?>
                        <div class="loop-article__content">
                            <h3 class="loop-article__content--title">
                                <a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>">
                                    <?php the_title(); ?>
                                </a>
                            </h3>
                            <?php do_action('get_intro', $postId); ?>
                            <span class="loop-article__content--author">
                                <?php
                                echo $companyName;
                                ?>
                            </span>
                        </div>
                    </article>
                    <?php
                }
            }

            wp_reset_postdata();
            ?>
            <?php

            $temp_query = $wp_query;
            $wp_query = NULL;
            $wp_query = $membersQuery;

            get_template_part('pagination');

            $wp_query = NULL;
            $wp_query = $temp_query;

            ?>
        </section>
        <section class="article-content">
            <article <?php post_class('article'); ?>>
                <?php
                the_content('');
                ?>
            </article>
        </section>
    <?php endwhile; ?>


</main>

<?php get_footer();