<?php

namespace SwedenBio\Theme;

/**
 * Members Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS


$title = get_field('vb-timeline_title') ?: 'Lorem Ipsum';

echo "<div class='".$cssClasses." block-with-padding sb_block'>";
    echo "<div class='sb_block__content'>";
        echo "<h2 class='sb_block__content--title'>$title</h2>";
            if (have_rows('vb-timeline_items')) {
                while(have_rows('vb-timeline_items')) {
                    the_row();
                    $year = get_sub_field('vb-timeline_year');
                    $title = get_sub_field('vb-timeline_titles');
                    $content = get_sub_field('vb-timeline_content');
                    echo "<div class='vm-block-timeline__item'>";
                        echo "<div class='vm-block-timeline__item--year'>";
                            echo $year;
                        echo "</div>";
                        echo "<div class='vm-block-timeline__item--content'>";
                            echo "<h3 class='post-title'>".$title."</h3>";
                            echo "<div>";
                                echo $content;
                            echo "</div>";
                        echo "</div>";
                    echo "</div>";
                }
            }
    echo "</div>";
echo "</div>";