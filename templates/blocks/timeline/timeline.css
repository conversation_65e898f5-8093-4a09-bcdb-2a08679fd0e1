div.vm-block-timeline .sb_block__content--title {
    color: #23252B;
    border-color: #23252B;
}
.vm-block-timeline__item {
    display: flex;
    margin-bottom: 30px;
    flex-wrap: wrap;
    border-bottom: 1px solid #94A0AF;
    padding-bottom: 30px;
}

@media screen and (min-width: 768px) {
    .vm-block-timeline__item {
        flex-wrap: nowrap;
    }
}

.vm-block-timeline__item--year {
    line-height: 34px;
    font-weight: 900;
    font-size: 24px;
    letter-spacing: 0px;
    color: #214CAC;
    width: 100%;
}

@media screen and (min-width: 768px) {
    .vm-block-timeline__item--year {
        width: 160px;
        flex: 0 0 auto;
        word-spacing: 160px;
    }
}

.vm-block-timeline__item--content .post-title {
    line-height: 34px;
    font-weight: 900;
    font-size: 24px;
    letter-spacing: 0px;
    color: #23252B;
    margin: 0;
    padding: 0;
}

.vm-block-timeline__item--content div{
    line-height: 22px;
    font-weight: 400;
    font-size: 16px;
    letter-spacing: 0.16px;
    color: #23252B;
    overflow: hidden;
}