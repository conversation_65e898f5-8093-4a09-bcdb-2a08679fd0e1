.sb_red_box {
    position: relative;
    background: url(../../assets/images/sean-pollock-PhYq704ffdA-unsplash.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}
.sb_red_box:before {
    content: "";
    background: rgba(197, 17, 98, 0.8);
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    position: absolute;
}
.sb_red_box__content {
    z-index: 2;
    position: relative;
}

.sb_red_box__content .sb_red_box__content--title {
    text-align: left;
    font-size: 12px;
    line-height: 24px;
    font-weight: 600;
    letter-spacing: 0.16px;
    color: #FFFFFF;
    text-transform: uppercase;
    padding-bottom: 20px;
    margin-bottom: 35px;
    border-bottom: 1px solid #fff;
}

@media screen and (min-width: 768px){
    .sb_red_box__content .sb_red_box__content--title {
        font-size: 16px;
    }
}

.sb_red_box__content--posts {
    margin: 0 -15px;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;
}
.sb_red_box__content--posts__post {
    flex: 1 0 auto;
    width: 100%;
    padding: 0 15px 15px 15px;
}
@media screen and (min-width: 768px) {
    .sb_red_box__content--posts__post {
        width: 33%;
    }
}
.sb_red_box__content--posts__post .box-post-thumbnail {
    height: 0;
    position: relative;
    padding-bottom: 57.1428571429%;
    overflow: hidden;
    margin-bottom: 15px;
}
.sb_red_box__content--posts__post .box-post-thumbnail img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}
.sb_red_box__content--posts__post .box-post-text {
    color: #fff;
    letter-spacing: 0.16px;
    line-height: 18px;
    font-size: 12px;
}

@media screen and (min-width: 768px){
    .sb_red_box__content--posts__post .box-post-text {
        font-size: 16px;
        line-height: 22px;
    }
}

.sb_red_box__content--button-wrapper {
    text-align: center;
    padding-top: 10px;
}