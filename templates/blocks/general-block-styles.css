.article-content {
    background: #fff;
}
.wp-block {
    max-width: initial;
}

.wp-block[data-align=wide] {
    max-width: 930px;
}

.edit-post-visual-editor__post-title-wrapper .editor-post-title {
    max-width: 930px;
    padding-left: 20px;
    padding-right: 20px;
}

@media screen and (min-width: 768px) {
    .edit-post-visual-editor__post-title-wrapper .editor-post-title
    {
        padding-left: 95px;
        padding-right: 95px;
    }
}

.article-content a:not(.btn), .is-root-container a:not(.btn) {
    color: #C51162;
    font-weight: 600;
    text-decoration: none;
}
.article-content .article > ul, .article-content .article > ol, .is-root-container > ul, .is-root-container > ol {
    list-style: none;
    position: relative;
}

.article-content .article > ul li, .article-content .article > ol li{
    position: relative;
    padding-left: 20px;
}

.article-content .article > ul li:before {
    content: '•';
    position: absolute;
    left: 0;
}

.article-content .article > ol li:before {
    content: counter(list-item);
    position: absolute;
    left: 0;
}

.article-content .article > p:first-child {
    margin-top: 0;
}
.article-content .article > *,
.is-root-container > *,
.article-content .article > .wp-block-group > .wp-block-group__inner-container > *,
.is-root-container > .wp-block > .wp-block-group > .wp-block-group__inner-container > *
{
    max-width: 930px;
    margin-left: auto;
    margin-right: auto;
    letter-spacing: 0.16px;
    line-height: 1.3;
    padding-left: 20px;
    padding-right: 20px;
}
@media screen and (min-width: 768px) {
    .article-content .article > *,
    .is-root-container > *,
    .article-content .article > .wp-block-group > .wp-block-group__inner-container > *,
    .is-root-container > .wp-block > .wp-block-group > .wp-block-group__inner-container > *
    {
        padding-left: 95px;
        padding-right: 95px;
        line-height: 1.4;
    }
}


.article-content .article > *.alignwide,
.is-root-container > *[data-align=wide]
{
    padding-left: 20px;
    padding-right: 20px;
}



.article-content .article .wp-block-image.alignwide, .wp-block[data-align=wide] > .wp-block-image {
    padding: 0;
}

.article-content .article > .wp-block-group > .wp-block-group__inner-container > *.alignwide,
.is-root-container > .wp-block > .wp-block-group > .wp-block-group__inner-container > *[data-align=wide],
.article-content .article > .wp-block-columns.alignwide,
.is-root-container > .wp-block[data-align=wide] > .wp-block-columns
{
    padding-right: 20px;
    padding-left: 20px;
}

.article-content .article > *.alignfull,
.is-root-container > *[data-align=full]
{
    padding-left: 0;
    padding-right: 0;
    max-width: 100%;
}

.article-content .article > .wp-block-group > .wp-block-group__inner-container > *.alignfull,
.is-root-container > .wp-block > .wp-block-group > .wp-block-group__inner-container > *[data-align=full],
.article-content .article > .wp-block-columns.alignfull,
.is-root-container > .wp-block[data-align=full] > .wp-block-columns
.article-content .article > .wp-block-rss.alignfull,
.is-root-container > div[data-type="core/rss"][data-align=full]
{
    padding-left: 20px;
    padding-right: 20px;
    max-width: 1390px;
    margin: auto;
}

.article-content .article .is-style-with-paddings,
.is-root-container .wp-block .is-style-with-paddings {
    padding-left: 20px!important;
    padding-right: 20px!important;
}

@media screen and (min-width: 768px) {
    .article-content .article > .wp-block-group > .wp-block-group__inner-container > *.alignfull,
    .is-root-container > .wp-block > .wp-block-group > .wp-block-group__inner-container > *[data-align=full],
    .article-content .article > .wp-block-columns.alignfull,
    .is-root-container > .wp-block[data-align=full] > .wp-block-columns,
    .article-content .article > .wp-block-rss.alignfull,
    .is-root-container > div[data-type="core/rss"][data-align=full]
    {
        padding-left: 95px;
        padding-right: 95px;
        max-width: 1390px;
        margin: auto;
    }
    .article-content .article .is-style-with-paddings,
    .is-root-container .wp-block .is-style-with-paddings {
        padding-left: 95px!important;
        padding-right: 95px!important;
    }
}

.alignfull.wp-block-image {
    margin-bottom: 0;
}

.wp-block-group {
    overflow: hidden;
}

.wp-block-image.alignfull img, .wp-block-image.alignwide img {
    vertical-align: top;
}

figure.wp-block-image {
    margin: 0;
    margin-bottom: 20px;
}

.article-content .article > .wp-block-group > .wp-block-group__inner-container,
.is-root-container > .wp-block-group > .wp-block-group__inner-container,
.article-content .article > .wp-block-media-text,
.is-root-container > .wp-block > .wp-block-media-text,
.article-content .article > .wp-block-columns,
.is-root-container > .wp-block > .wp-block-columns
{
    padding-top: 25px;
    padding-bottom: 25px;
}

@media screen and (min-width: 768px) {
    .article-content .article > .wp-block-group > .wp-block-group__inner-container,
    .is-root-container > .wp-block-group > .wp-block-group__inner-container,
    .article-content .article > .wp-block-columns,
    .is-root-container > .wp-block > .wp-block-columns
    {
        padding-top: 60px;
        padding-bottom: 60px;
    }
    .article-content .article > .wp-block-media-text,
    .is-root-container > .wp-block > .wp-block-media-text
    {
        padding-top: 60px;
        padding-bottom: 60px;
    }

    .article-content .article > .vm-block-underlined-text + .wp-block-media-text
    {
        padding-top: 30px;
    }

    .article-content .article > .wp-block-media-text.alignfull,
    .is-root-container > .wp-block[data-align="full"] > .wp-block-media-text
    {
        max-width: 1390px;
        margin: auto;
    }
}

.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .wp-block[data-align="full"] {
    margin-left: 0!important;
    margin-right: 0!important;
}

@media screen and (min-width: 930px) {
    .article-content .article > *.alignwide,
    .is-root-container > *[data-align=wide],
    .article-content .article > .wp-block-columns.alignwide,
    .is-root-container > .wp-block[data-align=wide] > .wp-block-columns
    {
        padding-left: 0;
        padding-right: 0;
    }
}

.article-content .article > blockquote {
    position: relative;
}
.article-content .article > blockquote:before {
    content: "";
    position: absolute;
    left: 15px;
    top: 0;
    width: 5px;
    background: #C51162;
    height: 100%;
}

@media screen and (min-width: 768px) {
    .article-content .article > blockquote:before {
        left: 75px;
    }
}

.wp-block-table.is-style-regular table td,  .wp-block-table.is-style-regular table th{
    border: 1px solid #6E7783;
}

.wp-block-table table td,  .wp-block-table table th{
    padding: 5px;
}

.wp-block-table table {
    border-collapse: collapse;
}

.wp-block-table figcaption, .blocks-gallery-caption, .wp-block-embed figcaption, .wp-block-image figcaption {
    color: #555d66;
    font-size: 13px;
    text-align: center;
    width: 100%;
}
.wp-block-table thead {
    border-bottom: 3px solid;
}

.wp-block-media-text__content h1,
.wp-block-media-text__content h2,
.wp-block-media-text__content h3,
.wp-block-media-text__content h4,
.wp-block-media-text__content h5,
.wp-block-media-text__content h6 {
    font-weight: 900;
    letter-spacing: 0px;
}

.wp-block-media-text__content > p, .wp-block-media-text__content {
    line-height: 1.3;
    font-weight: 400;
    font-size: 16px;
    letter-spacing: 0.16px;
}

.wp-block-media-text .wp-block-media-text__content {
    padding-left: 8%;
    padding-right: 0;
}

.wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {
    padding-left: 0;
    padding-right: 8%;
}

@media screen and (max-width: 980px) {
    .wp-block-media-text .wp-block-media-text__content {
        padding-left: 0;
        padding-right: 0;
    }

    .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {
        padding-left: 0;
        padding-right: 0;
    }

    .wp-block-media-text.is-stacked-on-mobile.has-media-on-the-right .wp-block-media-text__media,
    .wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__content {
        -ms-grid-column: 1;
        grid-column: 1;
        -ms-grid-row: 2;
        grid-row: 2;
    }
    .wp-block-media-text.is-stacked-on-mobile.has-media-on-the-right .wp-block-media-text__media {
        -ms-grid-column: 1;
        grid-column: 1;
        -ms-grid-row: 1;
        grid-row: 1;
    }
    .wp-block-media-text.is-stacked-on-mobile.has-media-on-the-right .wp-block-media-text__content {
        -ms-grid-column: 1;
        grid-column: 1;
        -ms-grid-row: 2;
        grid-row: 2;
    }

    .wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__media {
        margin-bottom: 20px;
    }

   .wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__content {
        -ms-grid-column: 1;
        grid-column: 1;
        -ms-grid-row: 2;
        grid-row: 2;
    }

    .wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__media {
        -ms-grid-column: 1;
        grid-column: 1;
        -ms-grid-row: 1;
        grid-row: 1;
    }

    .wp-block-media-text.is-stacked-on-mobile {
        -ms-grid-columns: 100%!important;
        grid-template-columns: 100%!important;
    }
}

.block-editor .wp-block-rss {
    padding: 0;
    list-style: none;
    margin: 0;
}

ul.wp-block-rss {
    list-style: none;
    padding: 0;
    margin: 0;
}

ul.wp-block-rss li {
    padding-left: 0!important;
}

ul.wp-block-rss li:before {
    display: none;
}

.wp-block-rss__item .wp-block-rss__item-author, .wp-block-rss__item .wp-block-rss__item-publish-date {
    color: #59636C;
    font-size: 10px;
    line-height: 16px;
    font-weight: 600;
    letter-spacing: 0.2px;
    text-transform: uppercase;
}


.wp-block-group.has-background {
    padding: 20px 30px;
    margin-top: 0;
    margin-bottom: 0;
}

ul.wp-block-rss li.wp-block-rss__item {
    margin-bottom: 20px;
}

ul.wp-block-rss .wp-block-rss__item-excerpt {
    line-height: 16px;
    font-weight: 600;
    font-size: 10px;
    letter-spacing: 0.2px;
    color: #59636C;
    text-transform: uppercase;
}

ul.wp-block-rss .wp-block-rss__item-title {
    line-height: 22px;
    font-weight: 400;
    font-size: 16px;
    letter-spacing: 0.16px;
    color: #23252B;
    margin-bottom: 5px;
}

ul.wp-block-rss .wp-block-rss__item {
    display: flex;
    flex-wrap: nowrap;
}

ul.wp-block-rss .wp-block-rss__item .wp-block-rss__item-img {
    flex: 0 0 auto;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    position: relative;
}

ul.wp-block-rss .wp-block-rss__item .wp-block-rss__item-img img{
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

ul.wp-block-rss .wp-block-rss__item-title a{
    line-height: inherit;
    font-weight: inherit;
    font-size: inherit;
    letter-spacing: inherit;
    color: inherit;
}

.wp-block-cover-image.has-background-dim.has-gray-background-color, .wp-block-cover.has-background-dim.has-gray-background-color,
.has-gray-background-color {
    background: #E5ECF2;
}
.wp-block-cover-image.has-background-dim.has-red-background-color, .wp-block-cover.has-background-dim.has-red-background-color,
.has-red-background-color {
    background: #C51162;
}
.wp-block-cover-image.has-background-dim.has-dark-red-background-color, .wp-block-cover.has-background-dim.has-dark-red-background-color,
.has-dark-red-background-color {
    background: #8E0038;
}
.wp-block-cover-image.has-background-dim.has-dark-blue-background-color, .wp-block-cover.has-background-dim.has-dark-blue-background-color,
.has-dark-blue-background-color {
    background: #002872;
}
.wp-block-cover-image.has-background-dim.has-blue-background-color, .wp-block-cover.has-background-dim.has-blue-background-color,
.has-blue-background-color {
    background: #214CAC;
}
.wp-block-cover-image.has-background-dim.has-dark-gray-background-color, .wp-block-cover.has-background-dim.has-dark-gray-background-color,
.has-dark-gray-background-color {
    background: #94A0AF;
}

.has-gray-color {
    color: #E5ECF2;
}
.has-red-color {
    color: #C51162;
}
.has-dark-red-color {
    color: #8E0038;
}
.has-dark-blue-color {
    color: #002872;
}
.has-blue-color {
    color: #214CAC;
}
.has-dark-gray-color {
    color: #94A0AF;
}

.has-gray-color .sb_block__content--title, .has-gray-color .wp-block-rss *{
    color: #E5ECF2!important;
    border-color: #E5ECF2!important;
}
.has-red-color .sb_block__content--title, .has-red-color .wp-block-rss *{
    color: #C51162!important;
    border-color: #C51162!important;
}
.has-dark-red-color .sb_block__content--title, .has-dark-red-color .wp-block-rss *{
    color: #8E0038!important;
    border-color: #8E0038!important;
}
.has-dark-blue-color .sb_block__content--title, .has-dark-blue-color .wp-block-rss *{
    color: #002872!important;
    border-color: #002872!important;
}
.has-blue-color .sb_block__content--title, .has-blue-color .wp-block-rss *{
    color: #214CAC!important;
    border-color: #214CAC!important;
}
.has-dark-gray-color .sb_block__content--title, .has-dark-gray-color .wp-block-rss *{
    color: #94A0AF!important;
    border-color: #94A0AF!important;
}

.wp-block-column {
    margin-bottom: 20px;
}

@media screen and (max-width: 600px) {
    .wp-block-media-text.has-media-on-the-right .wp-block-media-text__media {

    }
    .wp-block-media-text .wp-block-media-text__media {
        margin-bottom: 20px;
    }
}

.blocks-gallery-grid .blocks-gallery-image, .blocks-gallery-grid .blocks-gallery-item, .wp-block-gallery .blocks-gallery-image, .wp-block-gallery .blocks-gallery-item {
    flex-grow: 0;
}

.wp-block-gallery {
    overflow: hidden;
}

.wp-block-gallery.is-style-max-width,  .wp-block-gallery.is-style-max-width-and-no-margins{
    max-width: 1200px!important;
    margin: auto;
}

.wp-block-gallery .blocks-gallery-grid {
    margin: 0 -8px;
}

.wp-block-gallery.is-style-without-margins .blocks-gallery-grid, .wp-block-gallery.is-style-max-width-and-no-margins .blocks-gallery-grid {
    margin: 0;
}

.wp-block-gallery.is-style-default ul li.blocks-gallery-item, .wp-block-gallery.is-style-max-width ul li.blocks-gallery-item {
    margin: 0;
    padding: 0 8px 16px 8px;
}

.wp-block-gallery.is-style-without-margins, .wp-block-gallery.is-style-max-width-and-no-margins {
    padding: 0;
}

.wp-block-gallery.is-style-without-margins ul  li.blocks-gallery-item, .wp-block-gallery.is-style-max-width-and-no-margins li.blocks-gallery-item {
    padding: 0;
    margin: 0;
}

.wp-block-gallery.columns-1 li.blocks-gallery-item {
    width: 100%;
}
.wp-block-gallery.columns-2 li.blocks-gallery-item {
    width: 100%;
}
.wp-block-gallery.columns-3 li.blocks-gallery-item {
    width: 100%;
}
.wp-block-gallery.columns-4 li.blocks-gallery-item {
    width: 100%;
}
.wp-block-gallery.columns-5 li.blocks-gallery-item {
    width: 100%;
}
.wp-block-gallery.columns-6 li.blocks-gallery-item {
    width: 100%;
}
.wp-block-gallery.columns-7 li.blocks-gallery-item {
    width: 100%;
}
.wp-block-gallery.columns-8 li.blocks-gallery-item {
    width: 100%;
}

@media screen and (min-width: 500px) {
    .wp-block-gallery.columns-2 li.blocks-gallery-item {
        width: 50%;
    }
    .wp-block-gallery.columns-3 li.blocks-gallery-item {
        width: 50%;
    }
    .wp-block-gallery.columns-4 li.blocks-gallery-item {
        width: 50%;
    }
    .wp-block-gallery.columns-5 li.blocks-gallery-item {
        width: 50%;
    }
    .wp-block-gallery.columns-6 li.blocks-gallery-item {
        width: 50%;
    }
    .wp-block-gallery.columns-7 li.blocks-gallery-item {
        width: 50%;
    }
    .wp-block-gallery.columns-8 li.blocks-gallery-item {
        width: 50%;
    }
}

@media screen and (min-width: 768px) {
    .wp-block-gallery.columns-3 li.blocks-gallery-item {
        width: 33.3333333333%;
    }
    .wp-block-gallery.columns-4 li.blocks-gallery-item {
        width: 25%;
    }
    .wp-block-gallery.columns-5 li.blocks-gallery-item {
        width: 20%;
    }
    .wp-block-gallery.columns-6 li.blocks-gallery-item {
        width: 16.6666666667%;
    }
    .wp-block-gallery.columns-7 li.blocks-gallery-item {
        width: 14.2857142857%;
    }
    .wp-block-gallery.columns-8 li.blocks-gallery-item {
        width: 12.5%;
    }
}

@media screen and (max-width: 980px) {
    .wp-block-column {
        flex-basis: 100%!important;
        margin-left: 0!important;
        margin-right: 0!important;
    }
    .wp-block-columns {
        flex-wrap: wrap!important;
    }
}


.article-content .article > .vm-block-underlined-text + *:not(.has-background):not(.sb_block):not(.block-with-padding),
.is-root-container > .vm-block-underlined-text + *:not(.has-background):not(.sb_block):not(.block-with-padding),
.article-content .article > .vm-block-underlined-text + *:not(.has-background):not(.sb_block):not(.block-with-padding) > .wp-block-group__inner-container {
    margin-top: 0;
    padding-top: 0;
}

.wp-block-column p:first-child {
    margin-top: 0!important;
}

.wp-block-media-text__content :first-child {
    margin-top: 0!important;
}

.remove-bottom-space {
    padding-bottom: 0!important;
}