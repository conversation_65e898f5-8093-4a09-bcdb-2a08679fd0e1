<?php

namespace SwedenBio\Theme;

/**
 * Button Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE CONTENT, SET DEFAULTS

$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

$title = get_field('vm_button_title') ?: 'Lorem Ipsum';
$url = get_field('vm_button_url') ?: '#';
$newTab = get_field('vm_button_open_in_new') ?: false;
$center = get_field('vm_button_center') ?: false;
$small = get_field('vm_button_small') ?: false;
$style = get_field('vm_button_style') ?: 'default';

?>
<div <?php echo $center ? "style='text-align:center;'":''; ?> class="sb_block_button_wrapper">
    <a href="<?php echo $url; ?>" target="<?php echo $newTab ? '_blank':'_self'; ?>"  class="btn <?php echo $style.' '.($small ? 'small': '').' '.$cssClasses; ?>">
        <?php echo $title;
        if ($newTab) {
            ?>
            <svg xmlns="http://www.w3.org/2000/svg" width="14.977" height="14.977" fill="currentColor" viewBox="0 0 14.977 14.977"><path fill="none" d="M0,0H14.977V14.977H0Z"/><path d="M12.984,12.984H4.248V4.248H8.616V3H4.248A1.248,1.248,0,0,0,3,4.248v8.736a1.248,1.248,0,0,0,1.248,1.248h8.736a1.252,1.252,0,0,0,1.248-1.248V8.616H12.984ZM9.864,3V4.248H12.1L5.97,10.382l.88.88,6.134-6.134v2.24h1.248V3Z" transform="translate(-1.128 -1.128)"/></svg>
            <?php
        }
        ?>
    </a>
</div>
