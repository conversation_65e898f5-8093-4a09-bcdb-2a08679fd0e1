<?php

namespace SwedenBio\Theme;

/**
 * Members Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS


$title = get_field('vm-news-title') ?: __('Latest news from SwedenBIO', 'swedenbio');
$numberOfPosts = get_field('vm-news-number') ?: 4;
$numberOfColumns = get_field('vm-news-columns') ?: 2;

$columnsClass = $numberOfColumns == '2' ? "two-columns" : 'three-columns';

$newsPageId = get_option('page_for_posts');

$buttonTitle = get_field('vm_button_title');
$url = get_field('vm_button_url') ?: get_permalink($newsPageId);
$center = get_field('vm_button_center') ?: false;
$newTab = get_field('vm_button_open_in_new') ?: false;
$small = get_field('vm_button_small') ?: false;
$style = get_field('vm_button_style') ?: 'default';

$stickyPosts = get_field('vm-sticky-posts');
$count = $numberOfPosts;
if ($stickyPosts && is_array($stickyPosts)) {
    $numberOfStickyPosts = count($stickyPosts);
    if ($numberOfPosts >= $numberOfStickyPosts) {
        $count = $numberOfPosts - $numberOfStickyPosts;
    } else {
        $count = 0;
    }
}

$args = [
    'post_type' => 'post',
    'post_status' => 'publish',
    'posts_per_page' => $count,
    'ignore_sticky_posts' => 1,
    'meta_query' => [
        'relation' => 'OR',
        [
            'key'     => 'sb_hide-from-news-block',
            'value'   => '1',
            'compare' => '!='
        ],
        [
            'key'     => 'sb_hide-from-news-block',
            'compare' => 'NOT EXISTS'
        ],
    ]
];


$newsQuery =  new \WP_Query($args);


echo "<div class='".$cssClasses." sb_block'>";
    echo "<div class='sb_block__content'>";
        echo "<h2 class='sb_block__content--title'>$title</h2>";
        echo "<div class='sb_block__content--posts $columnsClass'>";
        if ($stickyPosts) {
            $i = 0;
            foreach ($stickyPosts as $st) {
                if ($i >= $numberOfPosts) {
                    break;
                }
                $image = get_the_post_thumbnail_url($st, 'news_list');
                echo "<div class='sb_block__content--posts__post'>";
                    echo "<a href='".get_permalink($st)."'>";
                        echo "<div class='news-image-wrapper' style='background-image: url(".$image.")'>";
                        echo "</div>";
                        echo "<h3 class='post-title'>".get_the_title($st)."</h3>";
                        do_action('get_intro', $st);
                    echo "</a>";
                echo "</div>";
                $i++;
            }
        }
            if ($count > 0 && $newsQuery->have_posts()) {
                while ($newsQuery->have_posts()) {
                    $newsQuery->the_post();
                    $postId = get_the_ID();
                    $image = get_the_post_thumbnail_url($postId, 'news_list');
                    echo "<div class='sb_block__content--posts__post'>";
                        echo "<a href='".get_permalink($postId)."'>";
                            echo "<div class='news-image-wrapper' style='background-image: url(".$image.")'>";
                            echo "</div>";
                            echo "<h3 class='post-title'>".get_the_title($postId)."</h3>";
                            echo "<span class='post-date'>".get_the_date('d M Y', $postId)."</span>";
                            do_action('get_intro', $postId);
                        echo "</a>";
                    echo "</div>";
                }
            }
        echo "</div>";
        if ($buttonTitle && $url) {
            ?>
            <div <?php echo $center ? "style='text-align:center;'":''; ?>>
                <a href="<?php echo $url; ?>" target="<?php echo $newTab ? '_blank':'_self'; ?>"  class="btn <?php echo $style.' '.($small ? 'small': ''); ?>">
                    <?php echo $title;
                    if ($newTab) {
                        ?>
                        <svg xmlns="http://www.w3.org/2000/svg" width="14.977" height="14.977" fill="currentColor" viewBox="0 0 14.977 14.977"><path fill="none" d="M0,0H14.977V14.977H0Z"/><path d="M12.984,12.984H4.248V4.248H8.616V3H4.248A1.248,1.248,0,0,0,3,4.248v8.736a1.248,1.248,0,0,0,1.248,1.248h8.736a1.252,1.252,0,0,0,1.248-1.248V8.616H12.984ZM9.864,3V4.248H12.1L5.97,10.382l.88.88,6.134-6.134v2.24h1.248V3Z" transform="translate(-1.128 -1.128)"/></svg>
                        <?php
                    }
                    ?>
                </a>
            </div>
            <?php
        }
    echo "</div>";
echo "</div>";
?>
