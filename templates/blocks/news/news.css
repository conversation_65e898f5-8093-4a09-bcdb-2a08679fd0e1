div.vm-block-news .sb_block__content--title, .sb_styled-title {
    color: inherit;
    border-color: inherit;
}

div.vm-block-news .sb_block__content--posts__post > a {
    color: inherit;
    font-weight: 400;
}

div.vm-block-news .sb_block__content--posts.two-columns .sb_block__content--posts__post {
    padding-bottom: 30px;
}

@media screen and (min-width: 768px) {
    div.vm-block-news .sb_block__content--posts.two-columns .sb_block__content--posts__post {
        width: 50%;
    }
}

div.vm-block-news .sb_block__content--posts__post .post-title {
    line-height: 24px;
    font-weight: 700;
    font-size: 16px;
    letter-spacing: 0.18px;
}

@media screen and (min-width: 768px) {
    div.vm-block-news .sb_block__content--posts__post .post-title {
        font-size: 18px;
    }
}

div.vm-block-news .sb_block__content--posts__post .post-date {
    display: block;
    line-height: 16px;
    font-weight: 600;
    font-size: 10px;
    letter-spacing: 0.2px;
    color: #59636C;
    text-transform: uppercase;
    margin: 10px 0;
}

div.vm-block-news .sb_block__content--posts__post .news-image-wrapper {
    position: relative;
    height: 0;
    width: 100%;
    padding-bottom: 55%;
    background-size: cover;
    background-position: center;
    margin-bottom: 15px;
}


div.vm-block-news .sb_block__content--posts__post .intro {
    line-height: 18px;
    font-weight: 400;
    font-size: 12px;
    letter-spacing: 0.16px;
    margin: 0;
    padding: 0;

    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* number of lines to show */
    -webkit-box-orient: vertical;
    line-clamp: 3;
}

@media screen and (min-width: 768px) {
    div.vm-block-news .sb_block__content--posts__post .intro {
        font-size: 16px;
        line-height: 22px;
    }
}