(function($){

    /**
     * initializeBlock
     *
     * Adds custom JavaScript to the block HTML.
     *
     * @date    15/4/19
     * @since   1.0.0
     *
     * @param   object $block The block jQuery element.
     * @param   object attributes The block attributes (only available when editing).
     * @return  void
     */
    var initializeBlock = function( $block ) {
        var $columns = $block.find('.sb_block__content--posts__post');
        $columns.each(function () {
            var $toggle = $(this).find('.vm-block-calendar-agenda-toggle');
            var $content = $(this).find('.sb_block__toggleable-content');
            var $column = $(this);
            $toggle.on("click", function (e) {
                e.preventDefault();
                e.stopPropagation();
                $content.slideToggle( "fast" );
                $toggle.toggleClass( "open" );
                $column.toggleClass("open");
            });
        });
    }

    // Initialize each block on page load (front end).
    $(document).ready(function(){
        $('.vm-block-calendar-agenda').each(function(){
            initializeBlock( $(this) );
        });
    });

    // Initialize dynamic block preview (editor).
    if( window.acf ) {
        window.acf.addAction( 'render_block_preview/type=vm-block-calendar-agenda', initializeBlock );
    }

})(jQuery);