<?php

namespace SwedenBio\Theme;

/**
 * Calendar Agenda Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$HTMLid = GutenbergBlocks::getBlocksHTMLID($block);
$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS

$preview_posts = [
    [
        "title" => "Lorem ipsum dolor sit amet",
        "image" => get_stylesheet_directory_uri().'/assets/images/agenda-placeholder.jpg',
        "ingress" => "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat.",
        "date" => "2 Jun 2020 | Stockholm",
        "is_exclusive" => true,
    ],
    [
        "title" => "Lorem ipsum dolor sit amet",
        "image" => get_stylesheet_directory_uri().'/assets/images/agenda-placeholder.jpg',
        "ingress" => "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat.",
        "date" => "2 Jun 2020 | Stockholm",
        "is_exclusive" => true,
    ],
    [
        "title" => "Lorem ipsum dolor sit amet",
        "image" => get_stylesheet_directory_uri().'/assets/images/agenda-placeholder.jpg',
        "ingress" => "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat.",
        "date" => "2 Jun 2020 | Stockholm",
        "is_exclusive" => true,
    ],
];

$title = get_field('vm_agenda-title') ?: 'Lorem ipsum dolor sit amet';
$events = get_field('vm_agenda-events');

?>

<div id="<?php echo esc_attr($HTMLid); ?>" class="<?php echo esc_attr($cssClasses); ?> sb_block">
    <div class="sb_block__content block-inner-padding">
        <h2 class='sb_block__content--title'><?php echo $title; ?></h2>
        <div class='sb_block__content--posts'>
                <?php
                    if ($events && is_array($events)) {
                        foreach ($events as $eventId) {
                            $startDate = get_field("sb-event_start_date", $eventId);
                            $place = get_field("sb-event_place", $eventId);
                            $isExclusive = get_field("sb-event_exclusive", $eventId);
                            $eventDatePlace = '';
                            if ($startDate) {
                                $sDate = new \DateTime($startDate);
                                $eventDatePlace .= $sDate->format('j M Y');
                                if ($place) {
                                    $eventDatePlace .= ' | ';
                                }
                            }
                            if ($place) {
                                $eventDatePlace .= $place;
                            }
                            echo "<div class='sb_block__content--posts__post'>";
                                echo "<a href='".get_permalink($eventId)."'>";
                                    echo "<div class='sb_block__post-image'>";
                                        echo get_the_post_thumbnail($eventId, 'news_list');
                                    echo "</div>";
                                    echo "<div class='vm-block-calendar-agenda-event-content'>";
                                        echo "<span class='vm-block-calendar-agenda-toggle' aria-label='".__("Close", 'swedenbio')."'></span>";
                                        echo "<h3 class='sb_block__post-title'>".get_the_title($eventId)."</h3>";
                                        echo "<span class='sb_block__post-date'>".$eventDatePlace."</span>";
                                        if ($isExclusive) {
                                            echo "<span class='vm-block-calendar-agenda-exclusive'>".__("Exclusively for members", 'swedenbio')."</span>";
                                        }
                                        echo "<div class='sb_block__toggleable-content'>";
                                            do_action('get_intro', $eventId);
                                        echo "</div>";
                                    echo "</div>";
                                echo "</a>";
                            echo "</div>";
                        }
                    } else {
                        foreach ($preview_posts as $pPost) {
                            echo "<div class='sb_block__content--posts__post'>";
                            echo "<a href='#'>";
                                echo "<div class='sb_block__post-image'>";
                                    echo "<img src='".$pPost['image']."'>";
                                echo "</div>";
                                echo "<div class='vm-block-calendar-agenda-event-content'>";
                                    echo "<span class='vm-block-calendar-agenda-toggle' aria-label='".__("Close", 'swedenbio')."'></span>";
                                    echo "<h3 class='sb_block__post-title'>".$pPost['title']."</h3>";
                                    echo "<span class='sb_block__post-date'>".$pPost['date']."</span>";
                                    if ($pPost['is_exclusive']) {
                                        echo "<span class='vm-block-calendar-agenda-exclusive'>".__("Exclusively for members", 'swedenbio')."</span>";
                                    }
                                    echo "<div class='sb_block__toggleable-content'>";
                                        echo "<p>";
                                            echo $pPost['ingress'];
                                        echo "<p>";
                                    echo "</div>";
                                echo "</div>";
                            echo "</a>";
                            echo "</div>";
                        }
                    }
                ?>
        </div>
    </div>
</div>