div.vm-block-calendar-agenda .sb_block__content--title {
    color: #23252B;
    border-bottom: 1px solid #23252B;
}

div.vm-block-calendar-agenda {
    position: relative;
    z-index: 3;
}

div.vm-block-calendar-agenda .sb_block__content--posts a {
    color: #23252B;
    display: block;
    position: relative;
}

div.vm-block-calendar-agenda .sb_block__content--posts__post {
    position: relative;
    z-index: 1;
}

div.vm-block-calendar-agenda .sb_block__content--posts__post.open {
    z-index: 2;
}

div.vm-block-calendar-agenda  .sb_block__post-title {
    letter-spacing: 0.18px;
    color: #23252B;
    line-height: 24px;
    font-weight: 700;
    font-size: 18px;
    margin: 0;
    padding: 0;
    padding-bottom: 10px;
}

div.vm-block-calendar-agenda  .sb_block__post-date {
    line-height: 16px;
    font-weight: 600;
    font-size: 10px;
    letter-spacing: 0.2px;
    color: #C51162;
    display: block;
    text-transform: uppercase;
    margin-bottom: 5px;
}

div.vm-block-calendar-agenda  .vm-block-calendar-agenda-exclusive {
    line-height: 16px;
    font-weight: 600;
    font-size: 10px;
    letter-spacing: 0.2px;
    color: #59636C;
    text-transform: uppercase;
    display: block;
}

div.vm-block-calendar-agenda  .sb_block__toggleable-content p {
    margin: 0;
    padding: 0;
    line-height: 22px;
    font-size: 16px;
    letter-spacing: 0.16px;
    font-weight: 400;
}

div.vm-block-calendar-agenda .sb_block__post-image {
    height: 0;
    position: relative;
    padding-bottom: 57.1428571429%;
    overflow: hidden;
    margin-bottom: 15px;
}

div.vm-block-calendar-agenda .sb_block__post-image img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: initial;
}
div.vm-block-calendar-agenda .vm-block-calendar-agenda-event-content {
    max-width: 85%;
    padding: 10px 30px 0px 10px;
    background: #fff;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
}

div.vm-block-calendar-agenda .sb_block__toggleable-content {
    display: none;
    overflow: hidden;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    padding: 10px 30px 10px 10px;
    background: #fff;
}

div.vm-block-calendar-agenda .vm-block-calendar-agenda-toggle {
    display: block;
    width: 25px;
    height: 25px;
    position: absolute;
    bottom: 0;
    right: 5px;
    background: url("../../../assets/images/expand_more-black.svg");
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
    transition: all .2s;
}

div.vm-block-calendar-agenda .vm-block-calendar-agenda-toggle.open {
    transform: rotate(180deg);
}