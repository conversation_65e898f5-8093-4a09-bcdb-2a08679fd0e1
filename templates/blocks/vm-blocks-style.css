/* <PERSON><PERSON><PERSON> in the backend uses floats for block alignment, so let's replicate that on the front end */

/* Clearing floats automatically so they don't go over the parent container */
.vm-block.alignleft:after .vm-block.alignright:after{
    content: "";
    visibility: hidden;
    display: block;
    height: 0;
    clear: both;
}
.vm-block.aligncenter{
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}
.vm-block.alignleft{
    float: left;
}
.vm-block.alignright{
    float: right;
}
.vm-block.alignjustify{
    text-align: justify;
}
.block-with-padding, .article-content .article > div.block-with-padding {
    padding: 25px 20px;
}

.block-with-vertical-padding, .article-content .article > div.block-with-vertical-padding {
    padding-top: 25px;
    padding-bottom: 25px;
}

.block-inner-padding, .article-content .article > .sb_block .block-inner-padding {
    padding: 25px 20px;
}

.block-inner-padding, .article-content .article > .sb_block.alignwide .block-inner-padding {
    padding: 25px 20px;
}

@media screen and (min-width: 768px) {
    .block-with-padding, .article-content .article > div.block-with-padding {
        padding: 60px 95px;
    }
    .block-with-vertical-padding, .article-content .article > div.block-with-vertical-padding {
        padding-top: 60px;
        padding-bottom: 60px;
    }
    .block-inner-padding, .article-content .article > .sb_block .block-inner-padding {
        padding: 60px 95px;
    }
    .block-inner-padding, .article-content .article > .sb_block.alignwide .block-inner-padding {
        padding: 60px 20px;
    }
}


.sb_block {
    position: relative;
}
.sb_block .sb_block__content--title, .sb_styled-title {
    text-align: left;
    font-size: 12px;
    line-height: 24px;
    font-weight: 600;
    letter-spacing: 0.16px;
    color: #FFFFFF;
    text-transform: uppercase;
    padding-bottom: 15px;
    margin-bottom: 25px;
    border-bottom: 1px solid #fff;
}

@media screen and (min-width: 768px){
    .sb_block .sb_block__content--title, .sb_styled-title {
        font-size: 16px;
    }
}

.sb_block__content {
    z-index: 2;
    position: relative;
    max-width: 1200px;
    margin: auto;
}
.sb_block__content--posts {
    margin: 0 -15px;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;
}
.sb_block__content--posts__post {
    flex: 0 0 auto;
    width: 100%;
    padding: 0 15px 15px 15px;
}

.sb_block__content--posts__post img{
    width: 100%;
}
@media screen and (min-width: 980px) {
    .sb_block__content--posts__post {
        width: 33.33333333%;
    }
}
.sb_block__content--button-wrapper {
    text-align: center;
    padding-top: 10px;
}

.sb_block a {
    text-decoration: none;
}

.sb_gray_box {
    position: relative;
    background: #E5ECF2;
}
.sb_gray_box .sb_block__content--title {
    color: #23252B;
    border-bottom: 1px solid #23252B;
}