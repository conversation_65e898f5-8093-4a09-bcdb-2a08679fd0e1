.vm-block-underlined-text .vm-block-content{
    text-align: left;
    font-size: 12px;
    line-height: 24px;
    font-weight: 600;
    letter-spacing: 0.16px;
    color: #23252B;
    text-transform: uppercase;
    padding-bottom: 15px;
    margin-bottom: 25px;
    border-bottom: 1px solid #23252B;
}

@media screen and (min-width: 768px){
    .vm-block-underlined-text .vm-block-content{
        font-size: 16px;
    }
}

.article-content .article > .vm-block-underlined-text.alignfull {
    margin-top: 25px;
}

@media screen and (min-width: 768px) {
    .article-content .article > .vm-block-underlined-text.alignfull {
        margin-top: 60px;
    }
}

.article-content .article > .vm-block-underlined-text.alignfull,
.is-root-container > [data-type='acf/vm-block-underlined-text'][data-align=full]
{
    padding-left: 20px;
    padding-right: 20px;
    max-width: 1390px;
}



.article-content .article > .vm-block-underlined-text.alignwide,
.is-root-container > [data-type='acf/vm-block-underlined-text'][data-align=wide]
{
    padding-left: 20px;
    padding-right: 20px;
}

@media screen and (min-width: 768px) {
    .article-content .article > .vm-block-underlined-text.alignfull,
    .is-root-container > [data-type='acf/vm-block-underlined-text'][data-align=full]
    {
        padding-left: 95px;
        padding-right: 95px;
        max-width: 1390px;
    }
}

@media screen and (min-width: 930px) {
    .article-content .article > .vm-block-underlined-text.alignwide,
    .is-root-container > [data-type='acf/vm-block-underlined-text'][data-align=wide]
    {
        padding-left: 0;
        padding-right: 0;
    }
}