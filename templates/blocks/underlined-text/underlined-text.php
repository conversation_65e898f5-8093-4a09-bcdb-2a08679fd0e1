<?php

namespace SwedenBio\Theme;

/**
 * Underlined Text Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$HTMLid = GutenbergBlocks::getBlocksHTMLID($block);
$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS

$text = get_field('vm_underlined_text') ? : ($is_preview ? '...' : '');

?>

<div id="<?php echo esc_attr($HTMLid); ?>" class="<?php echo esc_attr($cssClasses); ?>">
    <div class="vm-block-content">
        <?php echo $text; ?>
    </div>
</div>