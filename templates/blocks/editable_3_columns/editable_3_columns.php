<?php

namespace SwedenBio\Theme;

/**
 * Members Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS


$title = get_field('editable_3_columns-title') ?: 'Lorem Ipsum';
$bgImage = get_field('editable_3_columns-bg-image');
$bgColor = get_field('editable_3_columns-bg-color') ?: "#E5ECF2";
$bgColorOpacity = get_field('editable_3_columns-bg-color-opacity') ?: "1";

$textColor = get_field('editable_3_columns-text-color') ?: "#23252B";

$buttonTitle = get_field('vm_button_title');
$url = get_field('vm_button_url');
$newTab = get_field('vm_button_open_in_new') ?: false;
$small = get_field('vm_button_small') ?: false;
$style = get_field('vm_button_style') ?: 'default';

echo "<div class='".$cssClasses." block-with-padding alignfull sb_block' ".($bgImage ? "style='background-image: url(".$bgImage.")'" : "").">";
    echo "<div class='sb_block__background' style='background: ".$bgColor."; opacity: ".$bgColorOpacity.";'></div>";
    echo "<div class='sb_block__content' style='color: ".$textColor.";'>";
        echo "<h2 class='sb_block__content--title'>$title</h2>";
        echo "<div class='sb_block__content--posts'>";

            if (have_rows('editable_3_columns-content')) {
                while(have_rows('editable_3_columns-content')) {
                    the_row();
                    $image = get_sub_field('editable_3_columns-image');
                    $text = get_sub_field('editable_3_columns-text');
                    echo "<div class='sb_block__content--posts__post'>";
                        echo "<div class='box-post-thumbnail'>";
                            if ($image) {
                                echo wp_get_attachment_image($image, 'news_list');
                            }
                        echo "</div>";
                        echo "<div class='box-post-text'>";
                            echo $text;
                        echo "</div>";
                    echo "</div>";
                }
            }
        echo "</div>";
        if ($buttonTitle && $url) {
            ?>
            <div style='text-align:center'>
                <a href="<?php echo $url; ?>" target="<?php echo $newTab ? '_blank':'_self'; ?>"  class="btn <?php echo $style.' '.($small ? 'small': ''); ?>">
                    <?php echo $buttonTitle;
                    if ($newTab) {
                        ?>
                        <svg xmlns="http://www.w3.org/2000/svg" width="14.977" height="14.977" fill="currentColor" viewBox="0 0 14.977 14.977"><path fill="none" d="M0,0H14.977V14.977H0Z"/><path d="M12.984,12.984H4.248V4.248H8.616V3H4.248A1.248,1.248,0,0,0,3,4.248v8.736a1.248,1.248,0,0,0,1.248,1.248h8.736a1.252,1.252,0,0,0,1.248-1.248V8.616H12.984ZM9.864,3V4.248H12.1L5.97,10.382l.88.88,6.134-6.134v2.24h1.248V3Z" transform="translate(-1.128 -1.128)"/></svg>
                        <?php
                    }
                    ?>
                </a>
            </div>
        <?php
        }
    echo "</div>";
echo "</div>";
