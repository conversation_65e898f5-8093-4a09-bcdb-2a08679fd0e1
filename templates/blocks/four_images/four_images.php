<?php

namespace SwedenBio\Theme;

/**
 * Members Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS


echo "<div class='".$cssClasses." alignfull sb_block'>";
    echo "<div class='sb_block__content'>";
        if (have_rows('vb-four_images_items')) {
            while(have_rows('vb-four_images_items')) {
                the_row();
                $image = get_sub_field('vb-four_images_items-image');
               if (!empty($image['url'])) {
                   echo "<div class='image' style='background-image: url(".$image['url'].")'></div>";
               }
            }
        }
    echo "</div>";
echo "</div>";