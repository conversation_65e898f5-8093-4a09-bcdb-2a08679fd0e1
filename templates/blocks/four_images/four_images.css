.vm-block-four-images .sb_block__content {
    max-width: 100%;
}

.vm-block-four-images .sb_block__content .image {
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    width: 100%;
    padding-bottom: 46%;
}

@media screen and (min-width: 1024px) {
    .vm-block-four-images .sb_block__content {
        display: grid;
        grid-template-columns: 50% 25% 25%;
        grid-template-rows: 1fr 1fr;
        grid-auto-flow: column;
        height: 550px;
    }

    .vm-block-four-images .sb_block__content .image:first-child {
        grid-row-end: span 2;
    }

    .vm-block-four-images .sb_block__content .image:nth-child(2) {
        grid-column-end: span 2;
    }

    .vm-block-four-images .sb_block__content .image {
        width: auto;
        padding: 0;
    }
}