<?php

namespace SwedenBio\Theme;

/**
 * Members Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$HTMLid = GutenbergBlocks::getBlocksHTMLID($block);
$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS


$title = get_field('vm_members_block-title') ?: __('Our members', 'swedenbio'); // Våra medlemmar
$description = get_field('vm_members_block-description') ?: __('SwedenBIO brings together a variety of major players from the financial life science landscape.', 'swedenbio'); // SwedenBIO samlar en mångfald tunga aktörer från det finansiella life science-landskapet.
$posts = get_field('vm_members_block-posts');
$args = [
    'post_type' => 'page',
    'fields' => 'ids',
    'nopaging' => true,
    'meta_key' => '_wp_page_template',
    'meta_value' => 'templates/swedenbio-members.php'
];
$templates = get_posts( $args );
$class = get_field('vm_members_block-padding') ? "block-with-vertical-padding":'';
?>

<div id="<?php echo esc_attr($HTMLid); ?>" class="<?php echo esc_attr($cssClasses).' '.$class; ?> sb_block">
    <div class="sb_block__content">
        <h2 class='sb_block__content--title'><?php echo $title; ?></h2>
        <?php
        echo "<div class='count'>";
            $count = wp_count_posts('medlemmar');
            echo "<span>".$count->publish.'</span>'.__("members", 'swedenbio');
        echo "</div>";
        if ($description) {
            echo "<div class='description'>$description</div>";
        }
        ?>
        <div class='sb_members-list'>
                <?php
                    if ($posts && is_array($posts)) {
                        foreach ($posts as $postId) {
                            echo "<div class='sb_members-list__member'>";
                                echo get_the_title($postId);
                            echo "</div>";
                        }
                    }
                ?>
        </div>
        <?php
        if ($templates && isset($templates[0])) {
            $current_language = apply_filters('wpml_current_language', NULL);
            $translated_page_id = apply_filters('wpml_object_id', $templates[0], 'page', false, $current_language);
            if ($translated_page_id) {
                echo "<div class='sb_block__content--button-wrapper'>";
                    echo "<a href='".get_permalink($translated_page_id)."' class='btn'>".__("ALL Members", 'swedenbio')."</a>";
                echo "</div>";
            }
        }
        ?>
    </div>
</div>
