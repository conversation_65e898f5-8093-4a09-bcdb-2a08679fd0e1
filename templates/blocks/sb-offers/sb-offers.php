<?php

namespace SwedenBio\Theme;

/**
 * Members Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$HTMLid = GutenbergBlocks::getBlocksHTMLID($block);
$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS


$title = get_field('vm_sb-offers_title') ?: 'Lorem Ipsum';
$numberOfPosts = get_field('vm_sb-offers_number') ?: 3;


$buttonText = get_field('vm_sb-offers_buttontext') ?: __("More Member benefits", 'swedenbio');

$args = [
    'post_type' => 'page',
    'fields' => 'ids',
    'nopaging' => true,
    'meta_key' => '_wp_page_template',
    'meta_value' => 'templates/swedenbio-sb_offers.php'
];
$templates = get_posts( $args );


$queryArgs = [
    'post_type' => 'swedenbio_offers',
    'posts_per_page' => $numberOfPosts,
    'post_status' => 'publish',
    'paged' => 1
];


$membersQuery =  new \WP_Query($queryArgs);
?>

<div id="<?php echo esc_attr($HTMLid); ?>" class="<?php echo esc_attr($cssClasses); ?> sb_block">
    <div class="sb_block__content">
        <h2 class='sb_block__content--title'><?php echo $title; ?></h2>
        <div class='sb_members_posts-list'>
                <?php
                if ($membersQuery->have_posts()) {
                    while ($membersQuery->have_posts()) {
                        $membersQuery->the_post();
                        $postId = get_the_ID();
                        $intro = get_post_meta($postId, 'sb_intro', true);
                        $image = get_the_post_thumbnail($postId, 'uncropped_list');

                        echo "<a href='".get_permalink($postId)."' class='sb_members_posts-post'>";
                            echo "<div class='sb_members_posts-post__image'>";
                                echo $image ? $image : "<img src='".get_stylesheet_directory_uri()."/assets/images/swedenbiowhite.png'>";
                            echo "</div>";
                            echo "<div class='sb_members_posts-post__content'>";
                                echo "<h3 class='post-title'>".get_the_title($postId)."</h3>";
                                if ($intro) {
                                    echo "<p>".wp_trim_words($intro, 8)."</p>";
                                }
                                echo "<span class='author'>".get_the_author($postId)."</span>";
                            echo "</div>";
                        echo "</a>";
                    }
                    wp_reset_postdata();
                }
                ?>
        </div>
        <?php
        if ($templates && isset($templates[0])) {
            echo "<div class='sb_block__content--button-wrapper'>";
                echo "<a href='".get_permalink($templates[0])."' class='btn small'>".$buttonText."</a>";
            echo "</div>";
        }
        ?>
    </div>
</div>