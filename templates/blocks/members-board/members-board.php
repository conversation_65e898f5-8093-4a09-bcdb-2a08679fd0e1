<?php

namespace SwedenBio\Theme;

/**
 * Members Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$HTMLid = GutenbergBlocks::getBlocksHTMLID($block);
$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS


$title = get_field('vm_members-offers-board_title') ?: 'Lorem Ipsum';
$numberOfPosts = get_field('vm_members-offers-board_number') ?: 6;

$args = [
    'post_type' => 'page',
    'fields' => 'ids',
    'nopaging' => true,
    'meta_key' => '_wp_page_template',
    'meta_value' => 'templates/swedenbio-member_posts.php'
];
$templates = get_posts( $args );


$args2 = [
    'post_type' => 'page',
    'fields' => 'ids',
    'nopaging' => true,
    'meta_key' => '_wp_page_template',
    'meta_value' => 'templates/write-mb-post.php'
];
$templatesWrite = get_posts( $args2 );


$queryArgs = [
    'post_type' => 'sb_member_posts',
    'posts_per_page' => $numberOfPosts,
    'paged' => 1,
    'post_status' => 'publish',
];

$membersQuery =  new \WP_Query($queryArgs);
?>

<div id="<?php echo esc_attr($HTMLid); ?>" class="<?php echo esc_attr($cssClasses). ' alignfull'; ?> sb_block">
    <div class="sb_block__content">
        <h2 class='members-offers-board__title'><?php echo $title; ?></h2>
        <div class='members-offers-board__posts'>
                <?php
                if ($membersQuery->have_posts()) {
                    $rows = ceil(count($membersQuery->posts) / 3);
                    $lists  = array_chunk($membersQuery->posts, $rows);
                    $i = 1;
                    foreach ($lists as $list) {
                        echo "<div class='members-offers-board__posts--column'>";
                        if ($i == 6) {
                            $i = 1;
                        }
                        foreach ($list as $p) {
                            $postId = $p->ID;
							$companyName = get_post_meta($postId, 'sb_company-name', true);
                            echo "<a href='".get_permalink($postId)."' title='".get_the_title($postId)."' class='members-offers-board__post color-$i'>";
                                echo "<div class='sb_members_posts-post__content'>";
                                    echo "<h3 class='post-title'>".get_the_title($postId)."</h3>";
                                    echo "<span class='post_date'>".get_the_date('d M Y', $postId)."</span>";
                                   // echo "<span class='author'>".get_the_author($postId)."</span>";
                                    echo "<span class='author'>".$companyName."</span>";
                                echo "</div>";
                            echo "</a>";
                            $i++;
                        }
                        echo "</div>";
                    }
                    wp_reset_postdata();
                }
                ?>
        </div>
        <?php
        $postTemplate = (!is_user_logged_in() ? '/login/' : (($templatesWrite && isset($templatesWrite[0])) ? get_permalink($templatesWrite[0]) : false));

        if ($templates && isset($templates[0]) && $postTemplate) {
            echo "<div class='sb_block__content--button-wrapper'>";
                echo "<a href='".get_permalink($templates[0])."' class='btn'>".__('More posts on the notice board', 'swedenbio')."</a>";
                echo "<a href='$postTemplate' class='btn blue-outline'>".__('Write posts on the notice board', 'swedenbio')."</a>";
            echo "</div>";
        }
        ?>
    </div>
</div>