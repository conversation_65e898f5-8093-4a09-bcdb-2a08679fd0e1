<?php

namespace SwedenBio\Theme;

/**
 * Members Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS


$numberOfPosts = get_field('vm-news-category-number') ?: 3;
$category = get_field('vm-news-category');

if (!$category) {
    return;
}

$newsPageId = get_option('page_for_posts');

$buttonTitle = get_field('vm_button_title');
$url = get_field('vm_button_url') ?: get_permalink($newsPageId);
$center = get_field('vm_button_center') ?: false;
$newTab = get_field('vm_button_open_in_new') ?: false;
$small = get_field('vm_button_small') ?: false;
$style = get_field('vm_button_style') ?: 'default';

$stickyPosts = get_field('vm-sticky-posts');
$count = $numberOfPosts;
if ($stickyPosts && is_array($stickyPosts)) {
    $numberOfStickyPosts = count($stickyPosts);
    if ($numberOfPosts >= $numberOfStickyPosts) {
        $count = $numberOfPosts - $numberOfStickyPosts;
    } else {
        $count = 0;
    }
}

$args = [
    'post_type' => 'post',
    'post_status' => 'publish',
    'posts_per_page' => $count,
    'cat' => $category,
    'ignore_sticky_posts' => 1
];

$newsQuery =  new \WP_Query($args);


echo "<div class='".$cssClasses." sb_block'>";
    echo "<div class='sb_block__content'>";
        echo "<div class='sb_block__content--posts three-columns'>";
            if ($stickyPosts) {
                $i = 0;
                foreach ($stickyPosts as $st) {
                    if ($i >= $numberOfPosts) {
                        break;
                    }
                    $image = get_the_post_thumbnail_url($st, 'news_list');
                    echo "<div class='sb_block__content--posts__post'>";
                        echo "<a href='".get_permalink($st)."'>";
                            echo "<div class='news-image-wrapper' style='background-image: url(".$image.")'>";
                            echo "</div>";
                            echo "<h3 class='post-title'>".get_the_title($st)."</h3>";
                        echo "</a>";
                            if (has_excerpt($st)) {
                                echo apply_filters('the_content',get_the_excerpt($st));
                            } else {
                                do_action('get_intro', $st);
                            }

                    echo "</div>";
                    $i++;
                }
            }
            if ($count > 0 && $newsQuery->have_posts()) {
                while ($newsQuery->have_posts()) {
                    $newsQuery->the_post();
                    $postId = get_the_ID();
                    $image = get_the_post_thumbnail_url($postId, 'news_list');
                    echo "<div class='sb_block__content--posts__post'>";
                        echo "<a href='".get_permalink($postId)."'>";
                            echo "<div class='news-image-wrapper' style='background-image: url(".$image.")'>";
                            echo "</div>";
                            echo "<h3 class='post-title'>".get_the_title($postId)."</h3>";
                        echo "</a>";
                            if (has_excerpt($postId)) {
                                echo apply_filters('the_content',get_the_excerpt($postId));
                            } else {
                                do_action('get_intro', $postId);
                            }

                    echo "</div>";
                }
            }
        echo "</div>";
        if ($buttonTitle && $url) {
            ?>
            <div <?php echo $center ? "style='text-align:center;'":''; ?>>
                <a href="<?php echo $url; ?>" target="<?php echo $newTab ? '_blank':'_self'; ?>"  class="btn <?php echo $style.' '.($small ? 'small': ''); ?>">
                    <?php echo $buttonTitle;
                    if ($newTab) {
                        ?>
                        <svg xmlns="http://www.w3.org/2000/svg" width="14.977" height="14.977" fill="currentColor" viewBox="0 0 14.977 14.977"><path fill="none" d="M0,0H14.977V14.977H0Z"/><path d="M12.984,12.984H4.248V4.248H8.616V3H4.248A1.248,1.248,0,0,0,3,4.248v8.736a1.248,1.248,0,0,0,1.248,1.248h8.736a1.252,1.252,0,0,0,1.248-1.248V8.616H12.984ZM9.864,3V4.248H12.1L5.97,10.382l.88.88,6.134-6.134v2.24h1.248V3Z" transform="translate(-1.128 -1.128)"/></svg>
                        <?php
                    }
                    ?>
                </a>
            </div>
            <?php
        }
    echo "</div>";
echo "</div>";
?>
