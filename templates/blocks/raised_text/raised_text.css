.is-root-container > div[data-type="acf/vm-block-raised-text"][data-align='full'] .sb_block__content,
.article-content .article > .vm-block-raised-text.alignfull .sb_block__content{
    padding: 25px 20px;
}

@media screen and (min-width: 768px) {
    .is-root-container > div[data-type="acf/vm-block-raised-text"][data-align='full'] .sb_block__content,
    .article-content .article > .vm-block-raised-text.alignfull .sb_block__content{
        padding: 60px 95px;
    }
}

div.vm-block-raised-text .vm-block-raised-text__image {
    position: relative;
    height: 0;
    padding: 0;
    margin: 0;
    padding-bottom: 55%;
    width: 100%;
    z-index: 1;
    background: #94A0AF;
}

div.vm-block-raised-text .vm-block-raised-text__image.has_icon img {
    background: #94A0AF;
    mix-blend-mode: multiply;
}

div.vm-block-raised-text .vm-block-raised-text__image img{
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    display: block;
    object-fit: cover;
    width: 100%;
    height: 100%;
}

div.vm-block-raised-text .vm-block-raised-text__image a{
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 100%;
}

div.vm-block-raised-text .vm-block-raised-text__image .icon{
    position: absolute;
    z-index: 2;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: block;
    width: 70px;
    height: 70px;
    color: #fff;
}

div.vm-block-raised-text.alignfull .vm-block-raised-text__image .icon{
    width: 70px;
    height: 70px;
}

div.vm-block-raised-text .vm-block-raised-text__content {
    background: #fff;
    text-align: center;
    padding: 20px;
    width: 85%;
    margin: 0 auto;
    position: relative;
    margin-top: -20px;
    z-index: 2;
}

div.vm-block-raised-text .vm-block-raised-text__content .post-title {
    text-align: center;
    line-height: 28px;
    font-weight: 700;
    font-size: 16px;
    letter-spacing: 0.15px;
    color: #23252B;
    max-width: 650px;
    margin: auto;
    margin-bottom: 30px;
}
@media screen and (min-width: 768px){
    div.vm-block-raised-text .vm-block-raised-text__content .post-title {
        font-size: 20px;
    }
}

div.vm-block-raised-text .vm-block-raised-text__content p {
    text-align: center;
    line-height: 18px;
    font-weight: 400;
    font-size: 12px;
    letter-spacing: 0.16px;
    color: #23252B;
    max-width: 750px;
    margin: auto;
}

@media screen and (min-width: 768px){
    div.vm-block-raised-text .vm-block-raised-text__content p  {
        font-size: 16px;
        line-height: 22px;
    }
}

div.vm-block-raised-text.alignfull .sb_block__content {
    max-width: 1390px;
}

div.vm-block-raised-text.alignfull .vm-block-raised-text__content {
    margin-top: -20px;
}

div.vm-block-raised-text .vm-block-raised-text__content .btn{
    margin-top: 25px;
}

div.vm-block-raised-text.alignfull .vm-block-raised-text__content .post-title {
    text-align: center;
    line-height: 1.2;
    font-weight: 900;
    font-size: 28px;
    letter-spacing: 0px;
    color: #23252B;
}

@media screen and (min-width: 768px){
    div.vm-block-raised-text.alignfull .vm-block-raised-text__content .post-title {
        font-size: 40px;
        line-height: 54px;
    }

    div.vm-block-raised-text .vm-block-raised-text__content {
        margin-top: -50px;
    }

    div.vm-block-raised-text.alignfull .vm-block-raised-text__content {
        margin-top: -80px;
    }

    div.vm-block-raised-text.alignfull .vm-block-raised-text__image .icon{
        width: 170px;
        height: 170px;
    }
}