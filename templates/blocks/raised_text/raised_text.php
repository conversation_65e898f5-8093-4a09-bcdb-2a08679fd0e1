<?php

namespace SwedenBio\Theme;

/**
 * Members Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// ======== PREPARE HTML TAGS

$cssClasses = GutenbergBlocks::getBlocksCSSClasses($block);

// ======== PREPARE CONTENT, SET DEFAULTS


$title = get_field('vm-raised-text-title') ?: __('Latest news from SwedenBIO', 'swedenbio');
$text = get_field('vm-raised-text-content') ?: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut ultrices nibh in elementum blandit. Maecenas sed porta enim. Aenean sed bibendum dui, eget ultrices magna. Etiam facilisis risus ut luctus malesuada.';
$showIcon = get_field('vm-raised-text-show-icon');
$imageId = get_field('vm-raised-text-image');
$imageUrl = get_field('vm-raised-text-link');
$image = wp_get_attachment_url($imageId);


$buttonTitle = get_field('vm_button_title');
$url = get_field('vm_button_url');
$center = get_field('vm_button_center') ?: false;
$newTab = get_field('vm_button_open_in_new') ?: false;
$small = get_field('vm_button_small') ?: false;
$style = get_field('vm_button_style') ?: 'default';


echo "<div class='".$cssClasses." sb_block'>";
    echo "<div class='sb_block__content'>";

        echo "<div class='vm-block-raised-text__image ".($showIcon ? "has_icon":"")."'>";
            if ($image) {
                echo "<img src='$image'>";
            }
            if ($showIcon) {
                ?>
                    <div class="icon">
                        <svg xmlns='http://www.w3.org/2000/svg' fill='currentColor' viewBox='0 0 512 512'><path d='M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z' fill='none' stroke='currentColor' stroke-miterlimit='10' stroke-width='32'/><path d='M216.32 334.44l114.45-69.14a10.89 10.89 0 000-18.6l-114.45-69.14a10.78 10.78 0 00-16.32 9.31v138.26a10.78 10.78 0 0016.32 9.31z'/></svg>
                    </div>
                <?php
            }
            if ($imageUrl) {
                echo "<a href='$imageUrl' target='_blank'></a>";
            }
        echo "</div>";

        echo "<div class='vm-block-raised-text__content'>";
            if ($title) {
                echo "<h3 class='post-title'>".$title."</h3>";
            }
            if ($text) {
                echo "<p>".$text."</p>";
            }

            if ($buttonTitle && $url) {
                ?>
                <div <?php echo $center ? "style='text-align:center;'":''; ?>>
                    <a href="<?php echo $url; ?>" target="<?php echo $newTab ? '_blank':'_self'; ?>"  class="btn <?php echo $style.' '.($small ? 'small': ''); ?>">
                        <?php echo $buttonTitle;
                        if ($newTab) {
                            ?>
                            <svg xmlns="http://www.w3.org/2000/svg" width="14.977" height="14.977" fill="currentColor" viewBox="0 0 14.977 14.977"><path fill="none" d="M0,0H14.977V14.977H0Z"/><path d="M12.984,12.984H4.248V4.248H8.616V3H4.248A1.248,1.248,0,0,0,3,4.248v8.736a1.248,1.248,0,0,0,1.248,1.248h8.736a1.252,1.252,0,0,0,1.248-1.248V8.616H12.984ZM9.864,3V4.248H12.1L5.97,10.382l.88.88,6.134-6.134v2.24h1.248V3Z" transform="translate(-1.128 -1.128)"/></svg>
                            <?php
                        }
                        ?>
                    </a>
                </div>
                <?php
            }
        echo "</div>";
    echo "</div>";
echo "</div>";
?>
