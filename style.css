/*!
 Theme Name:   SwedenBio Theme
 Author:       Visionmate
 Version:      1.0.2
 Requires at least: 5.5
 */
html {
  line-height: 1.25;
}

* {
  box-sizing: border-box;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}
*:before, *:after {
  box-sizing: inherit;
}

body {
  font-size: 12px;
  font-size: 0.75rem;
  line-height: 1.25;
  position: relative;
  color: #23252B;
  background: #fff;
  font-family: "Inter", sans-serif;
}
@media screen and (min-width: 768px) {
  body {
    font-size: 16px;
    font-size: 1rem;
  }
}

.clearfix:after, .clearfix:before {
  content: "";
  display: table;
}
.clearfix:after {
  clear: both;
}

h2 {
  font-size: 24px;
  font-size: 1.5rem;
}
@media screen and (min-width: 768px) {
  h2 {
    font-size: 30px;
    font-size: 1.875rem;
  }
}

h3 {
  font-size: 18px;
  font-size: 1.125rem;
}
@media screen and (min-width: 768px) {
  h3 {
    font-size: 20px;
    font-size: 1.25rem;
  }
}

h4 {
  font-size: 16px;
  font-size: 1rem;
}

h5 {
  font-size: 15px;
  font-size: 0.9375rem;
}

h6 {
  font-size: 14px;
  font-size: 0.875rem;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 800;
  word-break: break-word;
}

h1 {
  font-size: 32px;
  font-size: 2rem;
  font-weight: 900;
}
@media screen and (min-width: 768px) {
  h1 {
    font-size: 40px;
    font-size: 2.5rem;
  }
}

p {
  word-break: break-word;
}

img {
  max-width: 100%;
  height: auto;
}

#wpadminbar {
  position: fixed;
}

p,
ul,
ol,
blockquote,
input,
textarea,
button,
table,
dl,
address,
pre,
select,
time {
  font-size: 12px;
  font-size: 0.75rem;
}
@media screen and (min-width: 768px) {
  p,
ul,
ol,
blockquote,
input,
textarea,
button,
table,
dl,
address,
pre,
select,
time {
    font-size: 16px;
    font-size: 1rem;
  }
}

blockquote {
  border-color: #002872;
  font-size: 16px;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (min-width: 768px) {
  blockquote {
    font-size: 25px;
    font-size: 1.5625rem;
  }
}

a {
  text-decoration: none;
  color: #23252B;
}

.main-header {
  min-height: 200px;
  background: transparent;
  width: 100%;
  padding: 0 20px;
  position: relative;
  padding-bottom: 140px;
}
@media screen and (min-width: 768px) {
  .main-header {
    padding: 0 95px;
    padding-bottom: 140px;
    min-height: 400px;
  }
}
.main-header__background {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100px;
  background: url("assets/images/header-pattern.jpg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
.main-header__background:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: #002872;
  opacity: 0.8;
  z-index: 2;
}
@media screen and (min-width: 768px) {
  .main-header__background {
    height: 100%;
  }
  .main-header__background:before {
    height: 100%;
  }
}
.main-header__additional-content {
  position: relative;
  z-index: 3;
  color: #fff;
  max-width: 730px;
}
.main-header__additional-content .page-title {
  font-size: 40px;
  line-height: 54px;
  font-weight: 900;
  letter-spacing: 0px;
}
.main-header__additional-content p {
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
  letter-spacing: 0.18px;
}
.main-header__wrapper {
  position: relative;
  z-index: 3;
  max-width: 1200px;
  margin: 0 auto 0 auto;
  padding-top: 30px;
}
.main-header__wrapper--menu-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 4;
}
.main-header a {
  color: #fff;
}
.main-header .logo-wrapper {
  color: #ffffff;
  max-width: 200px;
  display: block;
}
.main-header .main-menu {
  padding: 0 7px;
}
.main-header .main-menu ul {
  list-style: none;
}
.main-header .main-menu__items {
  margin: 0 -7px;
  display: flex;
  padding: 0;
  flex-wrap: wrap;
  align-items: stretch;
  justify-content: flex-end;
  height: 40px;
}
.main-header .main-menu__items > li {
  padding: 0 7px;
  height: 100%;
  display: none;
}
@media screen and (min-width: 1170px) {
  .main-header .main-menu__items > li {
    display: block;
  }
}
.main-header .main-menu__items > li.menu-item-with-search, .main-header .main-menu__items > li.hamburger-menu {
  display: block;
}
.main-header .main-menu__items > li.current-menu-ancestor > a, .main-header .main-menu__items > li.current-menu-item > a {
  color: rgba(255, 255, 255, 0.8);
}
.main-header .main-menu__items > li.menu-item-has-children {
  position: relative;
}
.main-header .main-menu__items > li.menu-item-has-children > a {
  padding-right: 22px;
  position: relative;
}
.main-header .main-menu__items > li.menu-item-has-children > a:before {
  content: "";
  height: 100%;
  position: absolute;
  right: 0;
  width: 20px;
  top: 0;
  background: url("assets/images/expand_more-24px.svg");
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.2s;
  transform: rotate(0deg);
}
.main-header .main-menu__items > li.menu-item-has-children.open > a:before {
  transform: rotate(180deg);
}
.main-header .main-menu__items > li.menu-item-has-children.open ul {
  display: block;
  padding: 0;
  margin: 0;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul {
  position: absolute;
  top: 100%;
  left: 7px;
  background: #E5ECF2;
  min-width: 400px;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul a {
  color: #23252B;
  font-size: 14px;
  font-size: 0.875rem;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul a:hover {
  color: #214CAC;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul > li {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  flex-wrap: nowrap;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul > li:last-child > a {
  padding: 25px 40px 25px 25px;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul > li > a {
  padding: 25px 40px 0px 25px;
  font-size: 16px;
  font-size: 1rem;
  font-weight: 600;
  flex: 0 0 auto;
  width: 50%;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul > li ul {
  display: none;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul > li > a > .menu-item-description {
  display: block;
  max-width: 150px;
  margin-top: 15px;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: 400;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul > li ul {
  display: block;
  padding: 25px;
  margin: 0;
  background: #fff;
  flex: 0 0 auto;
  width: 50%;
}
.main-header .main-menu__items > li.menu-item-has-children.open > ul > li ul a {
  color: #23252B;
  display: block;
  font-size: 16px;
  font-size: 1rem;
  padding-bottom: 10px;
}
.main-header .main-menu__items > li > a {
  display: flex;
  height: 100%;
  align-items: center;
  font-weight: 500;
}
.main-header .main-menu__items > li ul {
  display: none;
}
.main-header__buttons {
  margin-bottom: 20px;
}
@media screen and (min-width: 1080px) {
  .main-header__buttons.homepage {
    padding: 50px;
    max-width: 1200px;
    margin: auto;
  }
}
@media screen and (min-width: 1080px) {
  .main-header__buttons.homepage .sb-header-button {
    background: transparent;
    min-height: 68px;
    font-size: 17px;
    font-size: 1.0625rem;
    font-weight: 700;
  }
  .main-header__buttons.homepage .sb-header-button__icon, .main-header__buttons.homepage .sb-header-button__arrow {
    width: 55px;
  }
  .main-header__buttons.homepage .sb-header-button__icon {
    padding-left: 15px;
  }
  .main-header__buttons.homepage .sb-header-button__arrow {
    padding-right: 15px;
  }
  .main-header__buttons.homepage .sb-header-button__arrow img {
    display: block;
  }
}
.main-header__buttons.homepage .sb-header-button:hover {
  background: rgba(0, 40, 114, 0.8);
}
.main-header__buttons.homepage .sb-header-button__text--long-text {
  display: none;
}
.main-header__buttons.homepage .sb-header-button__text--short-text {
  display: block;
}
@media screen and (min-width: 1080px) {
  .main-header__buttons.homepage .sb-header-button__text--long-text {
    display: block;
  }
  .main-header__buttons.homepage .sb-header-button__text--short-text {
    display: none;
  }
}
@media screen and (min-width: 1080px) {
  .main-header__buttons.homepage .main-header__buttons--wrapper {
    margin: 0 -15px;
    justify-content: space-between;
    margin-top: 0px;
  }
}
@media screen and (min-width: 1080px) {
  .main-header__buttons.homepage .main-header__buttons--wrapper__button {
    width: 33.3333333333%;
    padding: 0 15px;
  }
}
.main-header__buttons--wrapper {
  display: flex;
  align-items: stretch;
  justify-content: center;
  flex-wrap: wrap;
  margin: 0 -7px;
  margin-top: 30px;
}
@media screen and (min-width: 768px) {
  .main-header__buttons--wrapper {
    align-items: stretch;
    justify-content: flex-end;
    margin-top: 0;
  }
}
.main-header__buttons--wrapper__button {
  padding: 0 7px;
  margin: 10px 0;
  width: auto;
}
@media screen and (min-width: 768px) {
  .main-header__buttons--wrapper__button {
    width: auto;
  }
}
.main-header__buttons--wrapper__button.only-mobile {
  display: none;
}
.main-header__buttons .sb-header-button {
  height: 100%;
  line-height: 24px;
  background: #002872;
  border: 1px solid #fff;
  border-radius: 70px;
  font-weight: 500;
  font-size: 16px;
  font-size: 1rem;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  color: #fff;
  min-height: 40px;
}
.main-header__buttons .sb-header-button:hover {
  background: rgba(0, 40, 114, 0.8);
}
.main-header__buttons .sb-header-button.active {
  background: rgba(255, 255, 255, 0.2);
}
@media screen and (min-width: 768px) {
  .main-header__buttons .sb-header-button {
    background: transparent;
  }
}
.main-header__buttons .sb-header-button__icon, .main-header__buttons .sb-header-button__arrow {
  flex: 0 0 auto;
  width: 25px;
  display: flex;
  align-items: center;
}
@media screen and (min-width: 768px) {
  .main-header__buttons .sb-header-button__icon, .main-header__buttons .sb-header-button__arrow {
    width: 35px;
  }
}
.main-header__buttons .sb-header-button__icon {
  padding-left: 10px;
}
.main-header__buttons .sb-header-button__arrow {
  color: #FFBB1F;
}
.main-header__buttons .sb-header-button__arrow img {
  display: none;
}
.main-header__buttons .sb-header-button__text {
  flex: 1 1 auto;
  padding: 5px 5px;
  min-width: 0;
}
@media screen and (min-width: 768px) {
  .main-header__buttons .sb-header-button__text {
    padding: 5px 15px;
  }
}
.main-header__buttons .sb-header-button__text--long-text {
  display: none;
}
.main-header__buttons .sb-header-button__text--short-text {
  display: block;
}

.page-template-default .sb-header-button.active {
  background: rgba(0, 40, 114, 0.6);
}

.home .main-header {
  padding-bottom: 0;
  min-height: 200px;
}
@media screen and (min-width: 768px) {
  .home .main-header {
    min-height: 300px;
  }
}

.page-template-blue-background .main-header__background {
  background: url("assets/images/NLSDays-HauteDef49.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  height: 100%;
}
.page-template-blue-background .main-header__background:before {
  background: #002872;
  height: 100%;
}
.page-template-blue-background .main-wrapper, .page-template-blue-background .article-content {
  background: transparent;
}
.page-template-blue-background .sb-header-button {
  background: transparent;
}
.page-template-blue-background .article-content {
  margin-top: -120px;
}
.page-template-blue-background .article-content > .article {
  background: transparent;
  position: relative;
}
.page-template-blue-background .article-content > .article:before {
  content: "";
  position: absolute;
  top: 100px;
  background: #002872;
  z-index: -1;
  height: 100%;
  width: 100%;
}
.page-template-blue-background .article-content {
  overflow: hidden;
}
.page-template-blue-background .article-content .article *:not(.has-text-color):not(.btn) {
  color: #fff;
}
.page-template-blue-background .article-content .article *:not(.btn) {
  border-color: #fff;
}
.page-template-blue-background .breadcrumbs {
  padding: 0 20px;
  max-width: 1390px;
}
.page-template-blue-background .breadcrumbs .breadcrumbs-item, .page-template-blue-background .breadcrumbs .separator {
  color: #94A0AF !important;
}
@media screen and (min-width: 768px) {
  .page-template-blue-background .breadcrumbs {
    padding: 0 95px;
  }
}

.page-template-bussines-and-finance .main-header__background {
  background: url("assets/images/sean-pollock-PhYq704ffdA-unsplash.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  height: 100%;
}
.page-template-bussines-and-finance .main-header__background:before {
  background: #C51162;
  height: 100%;
}
.page-template-bussines-and-finance .sb-header-button {
  background: transparent;
}

.page-template-bussines-and-finance .sb-header-button:hover {
  background: #C51162;
}

.page-template-bussines-and-finance .main-wrapper, .page-template-bussines-and-finance .article-content {
  background: transparent;
}
.page-template-bussines-and-finance .article-content {
  margin-top: -120px;
}
.page-template-bussines-and-finance .article-content > .article {
  background: transparent;
  position: relative;
}
.page-template-bussines-and-finance .breadcrumbs {
  padding: 0 20px;
  max-width: 1390px;
}
.page-template-bussines-and-finance .breadcrumbs .breadcrumbs-item, .page-template-bussines-and-finance .breadcrumbs .separator {
  color: #EAAEC2 !important;
}
@media screen and (min-width: 768px) {
  .page-template-bussines-and-finance .breadcrumbs {
    padding: 0 95px;
  }
}
.page-template-bussines-and-finance .breadcrumbs + .wp-block-columns {
  padding-top: 10px;
}

.page-template-contact .main-header__background {
  height: 100%;
}
.page-template-contact .main-header__background:before {
  height: 100%;
}
.page-template-contact .sb-header-button {
  background: transparent;
}
.page-template-contact .main-wrapper, .page-template-contact .article-content {
  background: transparent;
}
.page-template-contact .article-content {
  margin-top: -120px;
}
.page-template-contact .article-content > .article {
  background: transparent;
  position: relative;
}
.page-template-contact .breadcrumbs {
  padding: 0 20px;
  max-width: 1390px;
}
.page-template-contact .breadcrumbs .breadcrumbs-item, .page-template-contact .breadcrumbs .separator {
  color: #94A0AF !important;
}
@media screen and (min-width: 768px) {
  .page-template-contact .breadcrumbs {
    padding: 0 95px;
  }
}
.page-template-contact .breadcrumbs + .wp-block-columns {
  padding-top: 10px;
}

.page-template-paverkan .main-wrapper, .page-template-paverkan .article-content {
  background: transparent;
}
.page-template-paverkan .main-header__background {
  height: 100%;
}
.page-template-paverkan .main-header__background:before {
  height: 100%;
}
.page-template-paverkan .main-header {
  padding-bottom: 80px;
}
@media screen and (min-width: 680px) {
  .page-template-paverkan .main-header {
    padding-bottom: 140px;
  }
}
.page-template-paverkan .sb-header-button {
  background: transparent;
}
.page-template-paverkan .article-content {
  margin-top: -60px;
}
@media screen and (min-width: 680px) {
  .page-template-paverkan .article-content {
    margin-top: -120px;
  }
}
.page-template-paverkan .article-content > .article {
  background: transparent;
  position: relative;
}
.page-template-paverkan .article-header {
  padding: 0 20px;
  margin-bottom: 70px;
  margin-top: 0;
}
.page-template-paverkan .article-header .breadcrumbs {
  padding: 0;
}
.page-template-paverkan .article-header .breadcrumbs-item, .page-template-paverkan .article-header .separator {
  color: #94A0AF !important;
}
.page-template-paverkan .article-header img {
  display: block;
  margin-bottom: 0;
  padding: 0;
}
@media screen and (min-width: 930px) {
  .page-template-paverkan .article-header {
    padding: 0;
  }
}

.menu-item-description {
  display: none;
}

.secondary-menu {
  z-index: 999;
  position: fixed;
  top: 0;
  left: 0;
  background: #002872;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: none;
  padding: 60px 20px;
}
.secondary-menu.open {
  overflow: auto;
}
.secondary-menu__wrapper {
  max-width: 1200px;
  margin: 0 auto;
}
.secondary-menu a {
  color: #fff;
}
.secondary-menu .search-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  align-items: center;
}
.secondary-menu .search-row form {
  flex: 1 1 auto;
  margin-right: 40px;
  position: relative;
}
.secondary-menu .search-row input {
  width: 100%;
  line-height: 45px;
  padding: 0 50px 0 20px;
  height: 45px;
  border-radius: 24px;
  background: #fff;
  border: none;
}
.secondary-menu .search-row input::placeholder {
  line-height: 45px;
}
.secondary-menu .search-row .search-form__submit {
  background: transparent;
  border: none;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  line-height: 1;
}
.secondary-menu .secondary-menu-close {
  font-size: 40px;
  font-size: 2.5rem;
  line-height: 1;
}
.secondary-menu__items {
  list-style: none;
  position: relative;
  display: flex;
  margin: 0 -15px;
  flex-wrap: wrap;
  margin-top: 50px;
  padding: 0;
}
.secondary-menu__items > li {
  padding: 0 15px 15px 15px;
  width: 100%;
  flex: 0 0 auto;
}
@media screen and (min-width: 480px) {
  .secondary-menu__items > li {
    width: 50%;
  }
}
@media screen and (min-width: 768px) {
  .secondary-menu__items > li {
    width: 25%;
  }
}
.secondary-menu__items > li > a {
  font-weight: bold;
  font-size: 16px;
  font-size: 1rem;
}
@media screen and (min-width: 768px) {
  .secondary-menu__items > li > a {
    font-size: 20px;
    font-size: 1.25rem;
  }
}
.secondary-menu__items > li ul {
  list-style: none;
  font-size: 14px;
  font-size: 0.875rem;
  margin-top: 10px;
  padding: 0;
}
@media screen and (min-width: 768px) {
  .secondary-menu__items > li ul {
    font-size: 16px;
    font-size: 1rem;
  }
}
.secondary-menu__items > li ul li {
  margin-bottom: 5px;
}

.article-content.single {
  max-width: 930px;
  margin-left: auto;
  margin-right: auto;
  letter-spacing: 0.16px;
  line-height: 22px;
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 55px;
  padding-top: 0px;
}
@media screen and (min-width: 768px) {
  .article-content.single {
    padding-left: 95px;
    padding-right: 95px;
  }
}
.article-content.single.no-column .single-article-wrapper .article {
  padding-right: 0;
}
.article-content.single .single-article-wrapper {
  flex-wrap: wrap;
  display: flex;
  margin-bottom: 20px;
}
.article-content.single .single-article-wrapper .article > * {
  padding-right: 0;
  padding-left: 0;
}
@media screen and (min-width: 768px) {
  .article-content.single .single-article-wrapper {
    flex-wrap: nowrap;
  }
  .article-content.single .single-article-wrapper .article {
    width: 70%;
    padding-right: 20px;
    flex: 1 1 auto;
  }
  .article-content.single .single-article-wrapper .article p:first-child {
    margin-top: 0;
  }
  .article-content.single .single-article-wrapper .article-aside {
    width: 30%;
    order: 2;
  }
}

.article-aside {
  margin-bottom: 20px;
  width: 100%;
}
.article-aside--title {
  font-size: 16px;
  font-size: 1rem;
  line-height: 26px;
  font-weight: 600;
  letter-spacing: 0.16px;
  color: #23252B;
  text-transform: uppercase;
  padding-bottom: 15px;
  border-bottom: 1px solid #94A0AF;
}
.article-aside--date {
  letter-spacing: 0.2px;
  color: #59636C;
  text-transform: uppercase;
  font-size: 10px;
  font-size: 0.625rem;
  margin-bottom: 15px;
  display: block;
}
.article-aside--terms {
  margin: 0 -5px;
}
.article-aside--event-details {
  color: #214CAC;
  margin-top: 20px;
}
.article-aside--event-details .sub-title {
  font-weight: 600;
  line-height: 16px;
  font-size: 10px;
  font-size: 0.625rem;
  letter-spacing: 0.2px;
  text-transform: uppercase;
}
.article-aside--event-details div {
  letter-spacing: 0.18px;
  font-weight: 900;
  line-height: 24px;
  font-size: 18px;
  font-size: 1.125rem;
}
.article-aside .category {
  padding: 5px;
  display: inline-block;
}
@media screen and (min-width: 768px) {
  .article-aside .category {
    display: block;
  }
}
.article-aside .category a {
  color: #6E7783;
  font-weight: 400;
  letter-spacing: 0.2px;
  text-transform: uppercase;
  font-size: 10px;
  font-size: 0.625rem;
  line-height: 1;
  background: #E5ECF2 0% 0% no-repeat padding-box;
  border-radius: 34px;
  padding: 5px 15px;
  margin: 4px;
}

.cta-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.cta-container .btn {
  margin-right: 10px;
  margin-bottom: 10px;
}
.cta-container.categories {
  padding: 25px 0;
}
.cta-container.categories a {
  font-size: 10px;
  font-size: 0.625rem;
}

.footer {
  background: #23252B;
  padding: 40px 20px;
}
@media screen and (min-width: 768px) {
  .footer {
    padding: 40px 95px;
  }
}
.footer .apsis-subscribe-form {
  margin-top: 40px;
}
.footer .copyright {
  border-top: 1px solid #94A0AF;
  padding-top: 20px;
  font-size: 12px;
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: 0.12px;
}
.footer * {
  color: #94A0AF;
}
.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}
.footer-content .columns {
  display: flex;
  margin: 0 -15px;
  flex-wrap: wrap;
}
.footer-content__contact {
  width: 100%;
  padding: 15px;
}
@media screen and (min-width: 768px) {
  .footer-content__contact {
    width: 25%;
  }
}
.footer-content__information {
  width: 100%;
  padding: 15px;
}
@media screen and (min-width: 768px) {
  .footer-content__information {
    width: 75%;
  }
}
.footer-content .column {
  position: relative;
  padding: 15px;
  width: 100%;
}
@media screen and (min-width: 768px) {
  .footer-content .column {
    width: 25%;
  }
}
.footer-title {
  font-weight: 700;
  font-size: 14px;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.14px;
  border-bottom: 1px solid #94A0AF;
  padding-bottom: 15px;
  margin-bottom: 15px;
}
.footer ul, .footer ol {
  list-style: none;
  padding-left: 0;
}
.footer ul li, .footer ul li a, .footer ol li, .footer ol li a {
  line-height: 1.4;
}
.footer ul li, .footer ol li {
  margin-bottom: 10px;
}

.widget * {
  font-size: 12px;
  font-size: 0.75rem;
  line-height: 26px;
}
.widget .widget-title {
  font-size: 16px;
  font-size: 1rem;
  font-weight: 700;
  margin: 0;
  padding: 0;
  line-height: 26px;
}
.widget .textwidget, .widget .textwidget a {
  font-size: 16px;
  font-size: 1rem;
}
.widget .textwidget > *, .widget .textwidget a > * {
  font-size: inherit;
}

.content {
  position: relative;
  z-index: 2;
}

.archive-articles {
  max-width: 930px;
  padding: 20px 20px;
  margin: 0 auto;
}
@media screen and (min-width: 768px) {
  .archive-articles {
    padding: 50px 95px;
  }
}

body.home .article-header {
  margin-top: 0;
  display: none;
}
body.home .breadcrumbs {
  display: none;
}

.archive-header, .article-header {
  max-width: 930px;
  margin: 0 auto;
  margin-top: -140px;
}
.archive-header--content, .article-header--content {
  background: #fff;
  padding: 20px 20px;
}
@media screen and (min-width: 768px) {
  .archive-header--content, .article-header--content {
    padding: 50px 95px;
    padding-bottom: 25px;
  }
}
.archive-header .page-title, .article-header .page-title {
  letter-spacing: 0.4px;
  font-size: 30px;
  font-size: 1.875rem;
}
@media screen and (min-width: 768px) {
  .archive-header .page-title, .article-header .page-title {
    font-size: 40px;
    font-size: 2.5rem;
  }
}
.archive-header .intro, .article-header .intro {
  font-size: 16px;
  font-size: 1rem;
  line-height: 26px;
  letter-spacing: 0.18px;
  color: #23252B;
}
.archive-header .intro:last-child, .article-header .intro:last-child {
  margin-bottom: 0;
}
@media screen and (min-width: 768px) {
  .archive-header .intro, .article-header .intro {
    font-size: 18px;
    font-size: 1.125rem;
  }
}
.archive-header img, .article-header img {
  width: 100%;
  margin-bottom: 20px;
  padding: 0 20px;
}
@media screen and (min-width: 768px) {
  .archive-header img, .article-header img {
    padding: 0 95px;
  }
}

.loop-article {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 40px 0;
  border-bottom: 1px solid #E5ECF2;
}
@media screen and (min-width: 768px) {
  .loop-article {
    flex-wrap: nowrap;
  }
}
.loop-article .author {
  display: inline-block;
  margin-left: 5px;
}
.loop-article__image {
  width: 100%;
  flex: 0 0 auto;
  margin-bottom: 20px;
}
@media screen and (min-width: 768px) {
  .loop-article__image {
    width: 150px;
    margin-right: 30px;
  }
}
.loop-article__content {
  width: 100%;
}
.loop-article__content--date {
  color: #59636C;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  font-size: 10px;
  font-size: 0.625rem;
  line-height: 1;
  margin-bottom: 5px;
  display: block;
}
.loop-article__content--title {
  font-size: 24px;
  font-size: 1.5rem;
  margin-bottom: 15px;
}
.loop-article__content--categories {
  display: flex;
  align-items: center;
  margin: 0 -4px;
}
.loop-article__content--categories .category {
  letter-spacing: 0.2px;
  color: #6E7783;
  text-transform: uppercase;
  font-size: 10px;
  font-size: 0.625rem;
  line-height: 1;
  background: #E5ECF2 0% 0% no-repeat padding-box;
  border-radius: 34px;
  display: flex;
  padding: 5px 15px;
  margin: 4px;
}
.loop-article__content .intro {
  margin-top: 0;
}
@media screen and (min-width: 768px) {
  .loop-article__content {
    width: auto;
    flex: 1 1 auto;
    min-width: 0;
  }
}

.archive-pagination {
  padding: 30px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}
.archive-pagination > a, .archive-pagination > span {
  width: 35px;
  height: 35px;
  margin: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}
.archive-pagination > a.current, .archive-pagination > a:hover, .archive-pagination > span.current, .archive-pagination > span:hover {
  background: #002872;
  color: #fff;
  border-radius: 50%;
}
.archive-pagination > a.prev:hover, .archive-pagination > a.next:hover, .archive-pagination > span.prev:hover, .archive-pagination > span.next:hover {
  background: transparent;
  color: #23252B;
}

.breadcrumbs {
  display: flex;
  align-items: center;
  padding: 0 20px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}
@media screen and (min-width: 980px) {
  .breadcrumbs {
    padding: 0;
  }
}
.breadcrumbs-item, .breadcrumbs .separator {
  color: #94A0AF;
  font-weight: 500;
  font-size: 12px;
  font-size: 0.75rem;
  line-height: 1.2;
  margin-right: 10px;
  word-break: break-word;
  margin-bottom: 5px;
}

.alignfull + .alignfull {
  margin-top: 0;
}

.sb-events-list__year {
  letter-spacing: 0.16px;
  color: #23252B;
  text-transform: uppercase;
  font-size: 16px;
  font-size: 1rem;
  line-height: 26px;
  display: block;
  padding-bottom: 15px;
  margin-bottom: 30px;
  margin-top: 30px;
  border-bottom: 1px solid #94A0AF;
  font-weight: 600;
}
.sb-events-list__year:first-child {
  margin-top: 0;
}
.sb-events-list__event {
  padding: 25px 30px;
  display: flex;
  align-items: stretch;
  flex-wrap: wrap;
}
@media screen and (min-width: 768px) {
  .sb-events-list__event {
    flex-wrap: nowrap;
  }
}
.sb-events-list__event--date, .sb-events-list__event--content, .sb-events-list__event--buttons {
  width: 100%;
}
.sb-events-list__event--date {
  color: #214CAC;
  text-transform: uppercase;
  text-align: center;
}
@media screen and (min-width: 768px) {
  .sb-events-list__event--date {
    width: 120px;
    flex: 0 0 auto;
  }
}
.sb-events-list__event--date .date-month {
  display: block;
  letter-spacing: 0.2px;
  font-size: 10px;
  font-size: 0.625rem;
  line-height: 16px;
  font-weight: 600;
}
.sb-events-list__event--date .date-day {
  display: block;
  letter-spacing: 0px;
  font-size: 48px;
  font-size: 3rem;
  line-height: 50px;
  font-weight: 900;
}
.sb-events-list__event--content {
  margin-bottom: 10px;
}
@media screen and (min-width: 768px) {
  .sb-events-list__event--content {
    width: auto;
    flex: 1 1 auto;
  }
}
.sb-events-list__event--content .lists-title {
  letter-spacing: 0px;
  color: #23252B;
  font-weight: 900;
  line-height: 34px;
  font-size: 24px;
  font-size: 1.5rem;
  position: relative;
  top: -4px;
}
.sb-events-list__event--content .event-place {
  display: block;
  color: #23252B;
  font-weight: 400;
  letter-spacing: 0.16px;
  line-height: 22px;
  font-size: 16px;
  font-size: 1rem;
}
@media screen and (min-width: 768px) {
  .sb-events-list__event--buttons {
    width: 150px;
    flex: 0 0 auto;
    text-align: right;
  }
}
.sb-events-list__event--buttons .btn {
  margin-bottom: 10px;
  font-size: 10px;
  font-size: 0.625rem;
  letter-spacing: 0.2px;
  min-width: 0;
  padding: 0 15px;
  text-transform: none;
  width: 100%;
}
@media screen and (min-width: 768px) {
  .sb-events-list__event--buttons .btn {
    width: auto;
  }
}
.sb-events-list__event--buttons .event-type-own, 
.sb-events-list__event--buttons .event-type-partner,
.sb-events-list__event--buttons .event-type-external {
  letter-spacing: 0.2px;
  height: 22px;
  line-height: 22px;
}
.sb-events-list__event:nth-of-type(odd) {
  background: rgba(229, 236, 242, 0.5);
}

.lists-title {
  letter-spacing: 0px;
  color: #23252B;
  font-weight: 900;
  line-height: 30px;
  font-size: 20px;
  font-size: 1.25rem;
  position: relative;
}
@media screen and (min-width: 768px) {
  .lists-title {
    font-size: 24px;
    font-size: 1.5rem;
    line-height: 34px;
  }
}

div.gform_wrapper {
  overflow: hidden;
}
div.gform_wrapper .gf_progressbar {
  display: none;
}
div.gform_wrapper .gform_anchor {
  outline: none;
}
div.gform_wrapper .gform_anchor:focus {
  outline: none;
}
div.gform_wrapper .gf_progressbar_wrapper h3.gf_progressbar_title {
  margin: 0 !important;
}
div.gform_wrapper .top_label .gfield_label, div.gform_wrapper legend.gfield_label {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.16px;
  color: #23252B;
}
div.gform_wrapper .gform_title {
  text-align: left;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  letter-spacing: 0.16px;
  color: #23252B;
  text-transform: uppercase;
  padding-bottom: 20px;
  margin-bottom: 35px;
  border-bottom: 1px solid #23252B;
}
div.gform_wrapper select {
  background: transparent;
  color: #59636C;
  font-family: "Inter", sans-serif;
  border: 1px solid #59636C;
  border-radius: 19px;
  padding: 7px 20px !important;
  letter-spacing: 0.12px;
  line-height: 22px;
  font-size: 12px;
  outline: none !important;
  -webkit-appearance: none;
  -moz-appearance: none;
}
div.gform_wrapper div.styled-select {
  display: inline-block;
  position: relative;
}
div.gform_wrapper div.styled-select:before {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  content: "";
  width: 24px;
  height: 24px;
  background: url("assets/images/expand_more-black.svg");
  background-size: contain;
}
div.gform_wrapper div.styled-select select {
  padding-right: 30px !important;
  min-width: 250px !important;
  width: 100% !important;
}
div.gform_wrapper .address_country div.styled-select {
  width: 100%;
}
div.gform_wrapper .gfield_time_ampm select {
  max-width: initial !important;
  width: auto !important;
}
div.gform_wrapper h1, div.gform_wrapper h2, div.gform_wrapper h3, div.gform_wrapper h4, div.gform_wrapper h5, div.gform_wrapper h6 {
  font-weight: 800;
  padding: 20px 0;
}
div.gform_wrapper input:not([type=radio]):not([type=checkbox]):not([type=submit]):not([type=button]):not([type=image]):not([type=file]),
div.gform_wrapper textarea {
  background: #fff;
  line-height: 20px;
  padding: 10px 20px;
  border: 1px solid #E5ECF2;
  width: 100%;
  font-family: "Inter", sans-serif;
  outline: none !important;
}
div.gform_wrapper li.gfield ul.gfield_radio li > input[type=radio] + input[type=text] {
  width: 50% !important;
}
div.gform_wrapper select[multiple=multiple] {
  border: 1px solid #E5ECF2;
  background: #fff;
  outline: none !important;
}
div.gform_wrapper input {
  outline: none !important;
}
div.gform_wrapper .gfield_checkbox > li {
  position: relative;
}
div.gform_wrapper .gfield_checkbox > li input:first-child {
  position: absolute;
  opacity: 0;
}
div.gform_wrapper .gfield_checkbox > li input:first-child:checked + label:before {
  background: #C51162;
  background-image: url("assets/images/checkmark-outline.svg");
  background-size: contain;
}
div.gform_wrapper .gfield_checkbox > li label:before {
  content: "";
  margin-right: 10px;
  display: inline-block;
  vertical-align: top;
  width: 20px;
  height: 20px;
  background: #fff;
  border: 1px solid #E5ECF2;
}
div.gform_wrapper .gfield_radio > li {
  position: relative;
}
div.gform_wrapper .gfield_radio > li input:first-child {
  position: absolute;
  opacity: 0;
}
div.gform_wrapper .gfield_radio > li input:first-child:checked + label:before {
  background-image: url("assets/images/radio_button_dot.jpg");
  background-size: 18px;
  background-position: center;
  background-repeat: no-repeat;
}
div.gform_wrapper .gfield_radio > li label:before {
  content: "";
  margin-right: 10px;
  display: inline-block;
  vertical-align: middle;
  width: 30px;
  height: 30px;
  background: #fff;
  border: 1px solid #E5ECF2;
  border-radius: 50%;
}
div.gform_wrapper .gform_page_footer .button.gform_button,
div.gform_wrapper .gform_page_footer .button.gform_next_button,
div.gform_wrapper .gform_page_footer .button.gform_previous_button,
div.gform_wrapper .gform_footer .button.gform_button,
div.gform_wrapper .gform_footer .button.gform_next_button,
div.gform_wrapper .gform_footer .button.gform_previous_button {
  background: #C51162;
  border-radius: 24px;
  font-weight: 700;
  font-size: 14px;
  line-height: 17px;
  letter-spacing: 0.75px;
  color: #FFFFFF;
  text-transform: uppercase;
  border: 1px solid #C51162;
  cursor: pointer;
  min-width: 250px;
  height: 47px;
  font-family: "Inter", sans-serif;
}

.sb-members .sb_styled-title {
  color: #23252B;
  border-color: #e5ecf2;
  margin-bottom: 15px;
  margin-top: 40px;
}
.sb-members__terms > div {
  margin-bottom: 10px;
}
.sb-members__terms a {
  background: #59636C;
  border-radius: 34px;
  opacity: 1;
  display: inline-block;
  margin-right: 5px;
  margin-bottom: 5px;
  height: 22px;
  line-height: 22px;
  font-size: 10px;
  letter-spacing: 0.2px;
  color: #FFFFFF;
  padding: 0 15px;
}
.sb-members__terms a:hover, .sb-members__terms a.active {
  background: #343A41;
}
.sb-members__terms .parents a {
  background: #214CAC;
}
.sb-members__terms .parents a:hover, .sb-members__terms .parents a.active {
  background: #002872;
}
.sb-members__terms .children {
  display: none;
}
.sb-members__terms .children.show {
  display: block;
}
.sb-members__terms .children a {
  background: #C51162;
}
.sb-members__terms .children a:hover, .sb-members__terms .children a.active {
  background: #8E0038;
}
.sb-members__search {
  padding: 15px 0;
}
.sb-members__search--content {
  position: relative;
}
.sb-members__search--content svg {
  color: #6E7783;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
.sb-members__search--input {
  display: block;
  height: 30px;
  border: 1px solid #6E7783;
  border-radius: 34px;
  opacity: 1;
  letter-spacing: 0.2px;
  color: #6E7783;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 30px;
  width: 100%;
  padding: 0 40px 0 20px;
  font-weight: 600;
}
.sb-members__list {
  padding: 20px 0;
  padding-top: 0;
}
.sb-members__list--member {
  margin-bottom: 5px;
  padding: 15px 20px;
  background: rgba(229, 236, 242, 0.5);
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
@media screen and (min-width: 768px) {
  .sb-members__list--member {
    flex-wrap: nowrap;
  }
}
.sb-members__list--member.filtered, .sb-members__list--member.not-in-search {
  display: none;
}
.sb-members__list--member .lists-title {
  margin-bottom: 10px;
}
.sb-members__list--member .lists-title a {
  display: block;
}
.sb-members__list--member .website:hover {
  text-decoration: underline;
}
.sb-members__list--member__categories {
  width: 100%;
  flex: 0 1 auto;
  min-width: 0;
  letter-spacing: 0.2px;
  color: #59636C;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 16px;
  font-weight: 600;
  margin-top: 5px;
}
@media screen and (min-width: 768px) {
  .sb-members__list--member__categories {
    max-width: 200px;
    text-align: right;
    margin-top: 0;
  }
}

.sb-members_posts .loop-article, .sb-offers .loop-article {
  border: none;
  padding: 20px 0;
}
.sb-members_posts .loop-article.internal .loop-article__image, .sb-offers .loop-article.internal .loop-article__image {
  background: #FFBB1F;
}
.sb-members_posts .loop-article__image, .sb-offers .loop-article__image {
  width: 90px;
  height: 90px;
  background: #E5ECF2;
  border-radius: 50%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.sb-members_posts .loop-article__image img, .sb-offers .loop-article__image img {
  width: 80%;
  min-width: 0;
}
.sb-members_posts .loop-article__content--author, .sb-offers .loop-article__content--author {
  font-size: 10px;
  font-weight: 600;
  line-height: 16px;
  letter-spacing: 0.2px;
  color: #59636C;
  text-transform: uppercase;
}
.sb-members_posts .loop-article__content--title, .sb-members_posts .loop-article__content .intro, .sb-offers .loop-article__content--title, .sb-offers .loop-article__content .intro {
  margin-bottom: 10px;
}

#comments {
  font-family: "Inter", sans-serif;
  max-width: 930px;
  margin-left: auto;
  margin-right: auto;
  letter-spacing: 0.16px;
  line-height: 22px;
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 55px;
  padding-top: 20px;
}
@media screen and (min-width: 768px) {
  #comments {
    padding-left: 95px;
    padding-right: 95px;
  }
}
#comments textarea {
  background: #fff;
  line-height: 20px;
  padding: 10px 20px;
  border: 1px solid #E5ECF2;
  width: 100%;
  font-family: "Inter", sans-serif;
}
#comments input[type=submit] {
  background: #C51162;
  border-radius: 24px;
  font-weight: 700;
  font-size: 14px;
  line-height: 17px;
  letter-spacing: 0.75px;
  color: #FFFFFF;
  text-transform: uppercase;
  border: 1px solid #C51162;
  cursor: pointer;
  min-width: 250px;
  height: 47px;
  font-family: "Inter", sans-serif;
}
#comments label {
  text-align: left;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.16px;
  color: #23252B;
}
#comments .commentlist {
  padding: 0;
  list-style: none;
}
#comments .commentlist ul {
  list-style: none;
  padding: 0;
  margin-top: 30px;
}
#comments .commentlist li {
  padding: 20px;
  box-shadow: 0px 3px 12px #00000012;
  border-radius: 12px;
  background: #fff;
  margin-bottom: 30px;
}
#comments .commentlist li:last-child {
  margin-bottom: 0;
}
#comments .comment-reply-link {
  font-weight: 700;
  font-size: 14px;
}
#comments .sb-comment__author {
  line-height: 16px;
  font-weight: 600;
  font-size: 10px;
  letter-spacing: 0.2px;
  color: #59636C;
  text-transform: uppercase;
}
#comments #comments-title {
  padding: 20px 0;
}

article.sb_member_posts {
  margin-bottom: 20px;
}

.article-content .article p,
.article-content .article ul,
.article-content .article ol {
  word-break: break-word;
  margin-top: 10px;
  margin-bottom: 10px;
}

.page .article-content .article > :last-child:not(div):not(figure) {
  margin-bottom: 60px !important;
}

.page .article-content .article > .sb_block_button_wrapper:last-child {
  margin-bottom: 60px !important;
}

.page .article-content .article > :last-child:empty {
  display: none;
}

.article-content .article h1:not(.sb_block__content--title):not(.post-title):not(.event-title):not(.sb_block__post-title) {
  margin-top: 25px;
  margin-bottom: 10px;
}
.article-content .article h2:not(.sb_block__content--title):not(.post-title):not(.event-title):not(.sb_block__post-title) {
  margin-top: 25px;
  margin-bottom: 10px;
}
.article-content .article h3:not(.sb_block__content--title):not(.post-title):not(.event-title):not(.sb_block__post-title) {
  margin-top: 25px;
  margin-bottom: 10px;
}
.article-content .article h4:not(.sb_block__content--title):not(.post-title):not(.event-title):not(.sb_block__post-title) {
  margin-top: 25px;
  margin-bottom: 10px;
}
.article-content .article h5:not(.sb_block__content--title):not(.post-title):not(.event-title):not(.sb_block__post-title) {
  margin-top: 25px;
  margin-bottom: 10px;
}
.article-content .article h6:not(.sb_block__content--title):not(.post-title):not(.event-title):not(.sb_block__post-title) {
  margin-top: 25px;
  margin-bottom: 10px;
}

.apsis-subscribe-form {
  display: block;
  margin-bottom: 40px;
}
.apsis-subscribe-form input[type=text] {
  width: 100%;
  border: 1px solid #94A0AF;
  border-radius: 19px;
  color: #94A0AF;
  background: transparent;
  margin-bottom: 10px;
  padding: 0 15px;
  letter-spacing: 0.12px;
}
.apsis-subscribe-form .widget-title {
  margin-bottom: 10px;
}
.apsis-subscribe-form .btn {
  color: #fff;
  width: auto;
  min-width: 0;
}

.newsletter-popup-container {
  max-width: 1024px;
  width: 100%;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(197, 17, 98, 0.88);
  padding: 40px 60px;
  z-index: 900;
  color: #fff;
  display: none;
}
.newsletter-popup-container.open {
  display: block;
}
.newsletter-popup-container .close {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 30px;
  color: #fff;
}
.newsletter-popup-container .widget-title {
  text-align: center;
  font-size: 40px;
  line-height: 54px;
  font-weight: 900;
  letter-spacing: 0px;
  margin-bottom: 40px;
}
.newsletter-popup-container .apsis-subscribe-form {
  margin: 0;
}
.newsletter-popup-container .apsis-subscribe-form input[type=text] {
  max-width: 270px;
  background: #fff;
  text-align: center;
  margin: auto;
  display: block;
  margin-bottom: 10px;
}
.newsletter-popup-container .btn {
  margin: auto;
  display: block;
  background: #002872;
}
.newsletter-popup-container .description {
  text-align: center;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 0.16px;
  max-width: 760px;
  margin: auto;
  margin-top: 40px;
}

.wp-block-embed.is-type-video .wp-block-embed__wrapper {
  position: relative;
  height: 0;
  width: 100%;
  padding-bottom: 56.25%;
}
.wp-block-embed.is-type-video .wp-block-embed__wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
}

body #cookie-notice {
  z-index: 2147483647;
  font-family: "Inter", sans-serif;
  font-size: 14px;
}
body #cookie-notice .cn-button,
body #cookie-notice .cn-button:not(.cn-button-custom) {
  font-family: "Inter", sans-serif;
  font-size: 14px;
  text-transform: uppercase;
  background-color: #C51162!important;
}

body #cookie-law-info-bar {
  z-index: 2147483647;
}
body #cookie-law-info-bar .cli-plugin-button {
  border-radius: 24px;
}

iframe[name="us-entrypoint-buttonV2"] {
  z-index: 2147483646!important;
}

.search-no-results .article .search-form-wrapper {
  display: block;
  padding-bottom: 20px;
}
.search-no-results .article .search-form-wrapper .search-form {
  position: relative;
}
.search-no-results .article .search-form-wrapper .search-form input {
  background: #fff;
  line-height: 20px;
  padding: 10px 20px;
  padding-right: 50px;
  border: 1px solid #E5ECF2;
  width: 100%;
  font-family: "Inter", sans-serif;
  outline: none !important;
}
.search-no-results .article .search-form-wrapper .search-form .search-form__submit {
  background: transparent;
  border: none;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  line-height: 1;
}

div.um-login.um-logout, div.um, div.um-login {
  max-width: 930px !important;
}

.um-page-login .cta-container {
  margin-top: 30px;
}

.wp-block-group.columned-media .wp-block-media-text {
  margin-bottom: 10px;
}
@media screen and (max-width: 980px) {
  .wp-block-group.columned-media .wp-block-media-text__media {
    margin-right: 10px;
  }
}

@media screen and (max-width: 980px) {
  .mobile-no-margin-bottom {
    margin-bottom: 0;
  }
}

.wp-block-file .wp-block-file__button {
  display: inline-block;
}

@media screen and (max-width: 550px) {
  .wp-block-file *+.wp-block-file__button {
    margin-left: 0;
    margin-top: 5px;
    display: table;
  }
}
