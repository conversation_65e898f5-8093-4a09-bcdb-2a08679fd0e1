jQuery(document).ready(function($) {
    $('.main-menu__items > li.menu-item-has-children').on('mouseenter', function () {
        $(this).addClass('open');
    });
    $('.main-menu__items > li.menu-item-has-children').on('mouseleave', function () {
        $(this).removeClass('open');
    });

    $('.hamburger-menu-button').on("click", function (e) {
        e.preventDefault();
        $('.secondary-menu').slideDown('fast', function () {
            $('.secondary-menu').addClass('open');
        });
    });

    $('.secondary-menu-close').on("click", function (e) {
        e.preventDefault();
        $('.secondary-menu').removeClass('open');
        $('.secondary-menu').slideUp('fast');
    });

    $('.menu-item-with-search .search-button').on("click", function (e) {
        e.preventDefault();
        $('.secondary-menu').slideDown('fast', function () {
            $('.secondary-menu').addClass('open');
            $('.secondary-menu .search-form__input')[0].focus();
        });
    });

    function findParents($collection, $el, id) {
        var parent = $el.parent('div');
        $el.addClass('active');
        $collection.find('[data-parent="'+id+'"]').addClass('show');
        if (parent.hasClass('children')) {
            var data = $el.data('parent');
            var $newEl = $collection.find('[data-id="'+data+'"]')
            findParents($collection, $newEl, data);
        }
    }



    $('.sb-members__terms a').on("click", function (e) {
        e.preventDefault();
        $('.sb-members__terms a').removeClass('active');
        $('.sb-members__search--input').val('');
        var filter = $(this).data('filter');
        var id = $(this).data('id');
        $('.sb-members__terms .children').removeClass('show');
        $('.sb-members .sb_styled-title').text($(this).text());
        findParents($('.sb-members__terms'), $(this), id);
        if (filter == 'all') {
            $('.sb-members__list--member').removeClass('filtered');
        } else {
            $('.sb-members__list--member').removeClass('filtered').not('.'+filter).addClass('filtered');
        }
    });

    $('.sb-members__search--input').on('input', function() {
        var value = $(this).val();
        if (value) {
            var collection = $('.sb-members__list--member').not('.filtered');
            collection.removeClass('not-in-search').not('[data-title*="'+value.toLowerCase()+'"]').addClass('not-in-search');
        } else {
            $('.sb-members__list--member').removeClass('not-in-search');
        }
    });

    // var hash = window.location.hash;
    // if (hash && $('.sb-members__terms a').length > 0) {
    //     console.log($('.sb-members__terms').find('[data-filter="'+hash.replace('#','')+'"]'))
    //     $('.sb-members__terms').find('[data-filter="'+hash.replace('#','')+'"]').trigger('click');
    // }

    $('.newsletter-popup').on("click", function (e) {
        e.preventDefault();
        $('.newsletter-popup-container').addClass('open');
    });

    $('.newsletter-popup-container .close').on("click", function (e) {
        e.preventDefault();
        $('.newsletter-popup-container').removeClass('open');
    });
});

function MailingListValidation(SubscriberForm) {
    var counter = 0;
    for (i = 1; i <= SubscriberForm.pf_CounterMailinglists.value; i++) {
        var checkBoxName = "pf_MailinglistName" + i;
        if (document.getElementsByName(checkBoxName)[0].checked || document.getElementsByName(checkBoxName)[0].type == "hidden") counter++;
    }
    if (counter == 0) {
        alert("En eller flera e-postlistor krävs för detta formulär.");
        return false;
    }
}