.btn, .wp-block-buttons .wp-block-button__link {
    display: inline-block;
    background: #C51162;
    color: #fff;
    height: 33px;
    text-transform: uppercase;
    font-size: 10px;
    border-radius: 24px;
    line-height: 33px;
    padding: 0 45px;
    min-width: 220px;
    border: 1px solid #C51162;
    font-weight: 700;
    text-align: center;
    position: relative;
    text-decoration: none;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media screen and (min-width: 768px){
    .btn, .wp-block-buttons .wp-block-button__link {
        font-size: 14px;
        line-height: 45px;
        height: 45px;
    }
}

.btn img {
    vertical-align: middle;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}

.btn svg{
    vertical-align: text-bottom;
}

.btn:hover, .wp-block-buttons .wp-block-button__link:hover {
    background: #8E0038;
}
.btn.transparent-blue {
    background: transparent;
    border: 1px solid #fff;
}
.btn.transparent-blue:hover {
    background: rgba(0, 40, 114, 0.8);
}
.btn.blue {
    background: #214CAC;
    border: 1px solid #214CAC;
}
.btn.blue:hover {
    background: #002872;
}
.btn.yellow {
    background: #ffbb1f;
    border: 1px solid #ffbb1f;
}
.btn.yellow:hover {
    background: #c08600;
}
.btn.small {
    height: 30px;
    line-height: 30px;
    font-size: 10px;
    font-weight: 600;
}
.btn.white {
    background: #fff;
    border: 1px solid #94A0AF;
    color: #6b7480;
}
.btn.white-alt {
    background: #fff;
    border: 1px solid #94A0AF;
    color: #6b7480;
    height: 26px!important;
    line-height: 24px;
    margin-bottom: 0!important;
    margin-top: 10px;
}
.btn.white:hover {
    background: #E5ECF2;
}
.btn.transparent-gray,
.btn.transparent-grey {
    background: transparent;
    border: 1px solid #94A0AF;
    color: #94A0AF;
}
.btn.transparent-gray:hover,
.btn.transparent-grey:hover {
    background: #E5ECF2;
}
.btn.transparent-red {
    background: transparent;
    border: 1px solid #fff;
    color: #fff;
}
.btn.transparent-red:hover {
    background: rgba(142, 0, 56, 0.8);
}

.btn.blue-outline {
    background: #fff;
    border: 1px solid #002872;
    color: #002872;
}