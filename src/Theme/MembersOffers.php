<?php


namespace SwedenBio\Theme;



class MembersOffers
{
    const CPT = "sb_member_offers";
    const TEXTDOMAIN = "swedenbio";
    const ROLES_WITH_ACCESS = ['administrator', 'editor'];
    const CUSTOM_ROLE = 'sb_member';

    public function __construct()
    {
        add_action('init', [$this, 'addCPTandTaxonomy']);
        add_action('admin_init', [$this, 'grantAccess'],999);
        add_filter('sb_intro-location', [$this, 'addToACFLocation']);

        if (class_exists('ACF') && function_exists('acf_register_block_type')) {
            add_action('acf/init', [$this, 'register_block_offers']);
            add_action('acf/init', [$this, 'add_block_fields_offers']);
        }
    }

    public function addToACFLocation($array)
    {
        $array[][] = [
            'param' => 'post_type',
            'operator' => '==',
            'value' => self::CPT,
        ];

        return $array;
    }

    public function grantAccess()
    {
        foreach(self::ROLES_WITH_ACCESS as $role) {
            $r = get_role($role);

            if (!$r) {
                continue;
            }

            $r->add_cap( 'publish_members_offers',true);
            $r->add_cap( 'edit_members_offers', true);
            $r->add_cap( 'edit_others_members_offers', true);
            $r->add_cap( 'delete_members_offers', true );
            $r->add_cap( 'delete_others_members_offers', true );
            $r->add_cap( 'read_private_members_offers', true );
            $r->add_cap( 'edit_members_offer', true );
            $r->add_cap( 'delete_members_offer', true );
            $r->add_cap( 'read_members_offer', true );
            $r->add_cap( 'edit_published_members_offers', true );
            $r->add_cap( 'delete_published_members_offers', true );
            $r->add_cap( 'read_published_members_offers', true );
        }

        $member = get_role(self::CUSTOM_ROLE);

        if (!$member) {
            return;
        }
        
        $member->add_cap( 'edit_members_offers',true);
        $member->add_cap( 'edit_members_offer', true);
        $member->add_cap( 'read_members_offer', true);
        $member->add_cap( 'upload_files', true );
        $member->add_cap( 'read_published_members_offers', true );

    }

    public function addCPTandTaxonomy()
    {
        register_post_type(
            self::CPT,
            [
                'labels' => [
                    'name'          => __('Members Offers', self::TEXTDOMAIN),
                    'singular_name' => __('Members Offers', self::TEXTDOMAIN),
                ],
                'public'      => true,
                'has_archive' => false,
                'rewrite'     => ['slug' => 'erbjudande-fran-medlem'],
                'show_in_rest' => true,
                'supports' => ['title', 'editor', 'comments', 'revisions', 'trackbacks',  'excerpt', 'page-attributes', 'thumbnail', 'custom-fields', 'author'],
                'capability_type' => ['members_post', 'members_posts'],
                'menu_position' => 6,
                'menu_icon' => 'dashicons-welcome-widgets-menus',
                'capabilities' => [
                    'publish_posts' => 'publish_members_offers',
                    'edit_posts' => 'edit_members_offers',
                    'edit_others_posts' => 'edit_others_members_offers',
                    'delete_posts' => 'delete_members_offers',
                    'delete_others_posts' => 'delete_others_members_offers',
                    'read_private_posts' => 'read_private_members_offers',
                    'edit_post' => 'edit_members_offer',
                    'delete_post' => 'delete_members_offer',
                    'read_post' => 'read_members_offer',
                    'edit_published_pages' => 'edit_published_members_offers',
                    'delete_published_pages' => 'delete_published_members_offers',
                    'read_published_pages' => 'read_published_members_offers'
                ],
                'map_meta_cap' =>  true
            ]
        );
    }

    public function register_block_offers() {

        acf_register_block_type([
            'name'              => 'vm-block-members-offers',
            'title'             => __('Members Offers - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Members Offers for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/members-offers/members-offers.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/members-offers/members-offers.css',
            'category'          => 'widgets',
            'icon'              => 'editor-ul',
            'keywords'          => ['members', 'offers'],
            'supports'          => [
                'align' => false,
            ],
        ]);

    }

    public function add_block_fields_offers() {

        acf_add_local_field_group(
            [
                'key' => 'group_vm_members-offers',
                'title' => __('Members Offers', self::TEXTDOMAIN),
                'fields' =>
                    [
                        [
                            'key' => 'field_vm_members-offers_title',
                            'label' => __('Title', self::TEXTDOMAIN),
                            'name' => 'vm_members-offers_title',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_members-offers_buttontext',
                            'label' => __('Button text', self::TEXTDOMAIN),
                            'name' => 'vm_members-offers_buttontext',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_members-offers_number',
                            'label' => __('Number of posts', self::TEXTDOMAIN),
                            'name' => 'vm_members-offers_number',
                            'type' => 'number',
                        ],
                    ],
                'location' =>
                    [
                        [
                            [
                                'param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-members-offers',
                            ],
                        ],
                    ],
            ]
        );
    }

}