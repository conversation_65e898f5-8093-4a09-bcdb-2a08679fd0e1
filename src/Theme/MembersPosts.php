<?php


namespace SwedenBio\Theme;



class MembersPosts
{
    const CPT = "sb_member_posts";
    const TEXTDOMAIN = "swedenbio";
    const ROLES_WITH_ACCESS = ['administrator', 'editor'];
    const CUSTOM_ROLE = 'sb_member';
    const CUSTOM_ROLE_RU = 'sb_registered_users';

    public function __construct()
    {
        add_action('after_setup_theme', [$this, 'addRoles'], 1);
        add_action('init', [$this, 'addCPTandTaxonomy']);
        add_action('admin_init', [$this, 'grantAccess'],999);
        add_filter('sb_intro-location', [$this, 'addToACFLocation']);

        add_action('sb_logout_user_links', [$this, 'addButton']);
        add_action('um_profile_footer', [$this, 'addButton']);

        if (class_exists('ACF') && function_exists('acf_register_block_type')) {
            add_action('acf/init', [$this, 'register_block_offers_board']);
            add_action('acf/init', [$this, 'add_block_fields_offers_board']);
        }
    }

    public function addButton($args)
    {
        if (is_user_logged_in()) {
            $args2 = [
                'post_type' => 'page',
                'fields' => 'ids',
                'nopaging' => true,
                'meta_key' => '_wp_page_template',
                'meta_value' => 'templates/write-mb-post.php'
            ];
            $templatesWrite = get_posts( $args2 );
            if ($templatesWrite && isset($templatesWrite[0])) {
                echo '<div class="cta-container">';
                    echo "<a href='".get_permalink($templatesWrite[0])."' class='btn blue-outline'>".__('Write posts on the notice board', 'swedenbio')."</a>";
                echo '</div>';
            }
        }
    }

    public function addToACFLocation($array)
    {
        $array[][] = [
            'param' => 'post_type',
            'operator' => '==',
            'value' => self::CPT,
        ];

        return $array;
    }

    public function addRoles()
    {
        add_role(self::CUSTOM_ROLE, 'Member', array(
            'edit_members_posts' => true,
            'edit_members_post' => true,
            'read_members_post' => true,
            'upload_files' => true,
            'read_published_members_posts' => true,
            'read' => true
        ));

        add_role(self::CUSTOM_ROLE_RU, 'Registered user', array(
            'edit_members_posts' => true,
            'edit_members_post' => true,
            'read_members_post' => true,
            'upload_files' => true,
            'read_published_members_posts' => true,
            'read' => true
        ));
    }

    public function grantAccess()
    {
        foreach(self::ROLES_WITH_ACCESS as $role) {
            $r = get_role($role);

            $r->add_cap( 'publish_members_posts',true);
            $r->add_cap( 'edit_members_posts', true);
            $r->add_cap( 'edit_others_members_posts', true);
            $r->add_cap( 'delete_members_posts', true );
            $r->add_cap( 'delete_others_members_posts', true );
            $r->add_cap( 'read_private_members_posts', true );
            $r->add_cap( 'edit_members_post', true );
            $r->add_cap( 'delete_members_post', true );
            $r->add_cap( 'read_members_post', true );
            $r->add_cap( 'edit_published_members_posts', true );
            $r->add_cap( 'delete_published_members_posts', true );
            $r->add_cap( 'read_published_members_posts', true );
        }

    }

    public function addCPTandTaxonomy()
    {
        register_post_type(
            self::CPT,
            [
                'labels' => [
                    'name'          => __('Members posts', self::TEXTDOMAIN),
                    'singular_name' => __('Members posts', self::TEXTDOMAIN),
                ],
                'public'      => true,
                'has_archive' => false,
                'rewrite'     => ['slug' => 'anslagstavlan'],
                'show_in_rest' => true,
                'supports' => ['title', 'editor', 'comments', 'revisions', 'trackbacks',  'excerpt', 'page-attributes', 'thumbnail', 'custom-fields', 'author'],
                'capability_type' => ['members_post', 'members_posts'],
                'menu_position' => 6,
                'menu_icon' => 'dashicons-welcome-widgets-menus',
                'capabilities' => [
                    'publish_posts' => 'publish_members_posts',
                    'edit_posts' => 'edit_members_posts',
                    'edit_others_posts' => 'edit_others_members_posts',
                    'delete_posts' => 'delete_members_posts',
                    'delete_others_posts' => 'delete_others_members_posts',
                    'read_private_posts' => 'read_private_members_posts',
                    'edit_post' => 'edit_members_post',
                    'delete_post' => 'delete_members_post',
                    'read_post' => 'read_members_post',
                    'edit_published_pages' => 'edit_published_members_posts',
                    'delete_published_pages' => 'delete_published_members_posts',
                    'read_published_pages' => 'read_published_members_posts'
                ],
                'map_meta_cap' =>  true
            ]
        );
    }


    public function register_block_offers_board() {

        acf_register_block_type([
            'name'              => 'vm-block-members-offers-board',
            'title'             => __('Members Board - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Members Board for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'align' => 'full',
            'render_template'   => 'templates/blocks/members-board/members-board.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/members-board/members-board.css',
            'category'          => 'widgets',
            'icon'              => 'schedule',
            'keywords'          => ['members', 'offers'],
            'supports'          => [
                'align' => ['full'],
            ],
        ]);

    }

    public function add_block_fields_offers_board() {

        acf_add_local_field_group(
            [
                'key' => 'group_vm_members-offers-board',
                'title' => __('Members Board - Options', self::TEXTDOMAIN),
                'fields' =>
                    [
                        [
                            'key' => 'field_vm_members-offers-board_title',
                            'label' => __('Title', self::TEXTDOMAIN),
                            'name' => 'vm_members-offers-board_title',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_members-offers-board_number',
                            'label' => __('Number of posts', self::TEXTDOMAIN),
                            'name' => 'vm_members-offers-board_number',
                            'type' => 'number',
                        ],
                    ],
                'location' =>
                    [
                        [
                            [
                                'param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-members-offers-board',
                            ],
                        ],
                    ],
            ]
        );
    }

}