<?php
namespace SwedenBio\Theme;


class ThemeSetup
{
    const TEXTDOMAIN = 'swedenbio';

    public function __construct()
    {
        // set up wp
        add_action('init', [$this, 'cleanupWP']);
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('wp_print_styles', 'print_emoji_styles');
        add_action('after_setup_theme', [$this, 'themeSupports'], 1);
        add_action('wp_enqueue_scripts', [$this, 'scriptsAndStyles'], 20);

        add_action('widgets_init', [$this, 'registerSidebars']);
        add_action('init', [$this, 'registerMenus']);
        add_filter('wp_title', [$this,'wpTitle'], 1);
        add_filter('post_thumbnail_html', [$this, 'removeThumbnailDimensions']);
        add_filter('wp_get_attachment_link', [$this, 'removeThumbnailDimensions']);
        add_action('admin_enqueue_scripts', [$this, 'adminScripts']);


        add_filter('get_the_archive_title', [$this, 'editArchiveTitle']);
        // Remove the WordPress version from RSS feed
        add_filter('the_generator', '__return_false');
        add_action('get_intro', [$this, 'renderArticleIntro'], 10, 2);
        add_action('get_browser-title', [$this, 'browserTitle']);
        add_filter('walker_nav_menu_start_el', [$this, 'addDescriptionToMenuItems'], 10, 4 );
        add_action('get_breadcrumbs', [$this, 'getBreadcrumbs']);
        add_action('sb_header_content', [$this, 'additionalHeaderContent']);
        add_shortcode('sb_newsletter', [$this, 'newsletterForm']);
        add_filter( 'gform_field_content', [$this, 'selectMarkup'], 10, 2 );

        add_filter( 'the_content', [$this, 'fixSpaces'], 1 );

        $this->registerImageSizes();
        add_filter('image_size_names_choose', [$this, 'addImageSizesToAdmin']);

        add_filter( 'login_url', [$this, 'changeLoginUrl']);

        add_action('pre_get_posts', [$this, 'ignoreStickyPosts']);
    }

    public function ignoreStickyPosts($query)
    {
        if (is_home() && $query->is_main_query() && !is_admin()) {
            $query->set('ignore_sticky_posts', true);
        }
    }

    public function changeLoginUrl($link)
    {
        return "/login/";
    }

    public function fixSpaces($content)
    {
        if ( is_singular() && in_the_loop() && is_main_query() ) {
            $noSpaces = preg_replace('/(&nbsp;)/u', ' ', $content);
            return preg_replace('/[^\S\t\n\r]/u', ' ', $noSpaces);
        }

        return $content;
    }

    public function selectMarkup( $field_content, $field ) {
        if ($field->type === 'select' || $field->type === 'address') {
            $field_content = str_replace( "<select", "<div class='styled-select'><select", $field_content );
            $field_content = str_replace( "</select>", "</select></div>", $field_content );
        }

        return $field_content;
    }

    public function additionalHeaderContent() {
        if (is_page_template('templates/blue-background.php') || is_page_template('templates/bussines-and-finance.php') || is_page_template('templates/paverkan.php') || is_page_template('templates/contact.php')) {
            $intro = get_post_meta(get_the_ID(), 'sb_intro', true);
            echo "<div class='main-header__additional-content'>";
                echo "<h1 class='page-title'>".get_the_title()."</h1>";
                if ($intro) {
                    echo "<p>".$intro."</p>";
                }
            echo "</div>";
        }
    }


    public function addDescriptionToMenuItems($item_output, $item, $depth, $args)
    {
        if ( !empty( $item->description ) ) {
            $item_output = str_replace( $args->link_after . '</a>', '<span class="menu-item-description">' . $item->description . '</span>' . $args->link_after . '</a>', $item_output );
        }

        return $item_output;
    }


    public function editArchiveTitle($title)
    {
        if (is_search()) {
            return sprintf(__('Search results for: <em>%s</em>', self::TEXTDOMAIN), get_search_query());
        }

        if (!is_front_page() && is_home()) {
            $pageForPosts = get_option('page_for_posts');
            return get_the_title($pageForPosts);
        }
        return $title;
    }

    public function renderArticleIntro($postID = 0, $return = false)
    {
        if ($postID == 0) {
            global $post;
            $postID = $post->ID;
        }
        $intro = get_post_meta($postID, 'sb_intro', true);
        if (!empty($intro)) {
            $html = '<p class="intro">'. $intro.'</p>';
            if (!$return) {
                echo $html;
            } else {
                return $html;
            }
        }
    }

    public function getBreadcrumbs()
    {
        $separatorChar = '>';
        echo "<div class='breadcrumbs'>";
            echo "<a href='".home_url()."' class='breadcrumbs-item'>".__("Start page", self::TEXTDOMAIN)."</a>";
            echo "<span class='separator'>$separatorChar</span>";
            if (is_archive()) {
                echo "<span class='breadcrumbs-item'>".get_the_archive_title()."</span>";
            }

            if (is_search()) {
                echo "<span class='breadcrumbs-item'>".__("Search results:", self::TEXTDOMAIN)." ".get_search_query()."</span>"; // Sökresultat
            }

            if (!is_front_page() && is_home()) {
                $pageForPosts = apply_filters('wpml_object_id', get_option('page_for_posts'), 'page');
                echo "<span class='breadcrumbs-item'>".get_the_title($pageForPosts)."</span>";
            }
            if (is_singular(['post'])) {
                $pageForPosts = apply_filters('wpml_object_id', get_option('page_for_posts'), 'page');
                if ($pageForPosts) {
                    echo "<a href='".get_permalink($pageForPosts)."' class='breadcrumbs-item'>".get_the_title($pageForPosts)."</a>";
                    echo "<span class='separator'>$separatorChar</span>";
                }
                echo "<span class='breadcrumbs-item'>".get_the_title()."</span>";
            }
            if (is_singular(['sb_events', 'medlemmar', 'sb_member_posts', "sb_member_offers", "swedenbio_offers"])) {
                $template = 'templates/swedenbio-events.php';
                if (is_singular(['medlemmar'])) {
                    $template = 'templates/swedenbio-members.php';
                }
                if (is_singular(['sb_member_posts'])) {
                    $template = 'templates/swedenbio-member_posts.php';
                }
                if (is_singular(['sb_member_offers'])) {
                    $template = 'templates/swedenbio-members_offers.php';
                }
                if (is_singular(['swedenbio_offers'])) {
                    $template = 'templates/swedenbio-sb_offers.php';
                }
                $args = [
                    'post_type' => 'page',
                    'fields' => 'ids',
                    'nopaging' => true,
                    'meta_key' => '_wp_page_template',
                    'meta_value' => $template,
                    'suppress_filters' => false // Allow WPML to filter the query
                ];
                $listingTemplate = get_posts($args);
                if ($listingTemplate && isset($listingTemplate[0])) {
                    $listingTemplateId = apply_filters('wpml_object_id', $listingTemplate[0], 'page');
                    $templateAncestors = get_post_ancestors($listingTemplateId);
                    if ($templateAncestors) {
                        $reverseAncestors = array_reverse($templateAncestors);
                        foreach ($reverseAncestors as $templateAncestor) {
                            $translatedAncestorId = apply_filters('wpml_object_id', $templateAncestor, 'page');
                            echo "<a href='".get_permalink($translatedAncestorId)."' class='breadcrumbs-item'>".get_the_title($translatedAncestorId)."</a>";
                            echo "<span class='separator'>$separatorChar</span>";
                        }
                    }
                    echo "<a href='".get_permalink($listingTemplateId)."' class='breadcrumbs-item'>".get_the_title($listingTemplateId)."</a>";
                    echo "<span class='separator'>$separatorChar</span>";
                }
                echo "<span class='breadcrumbs-item'>".get_the_title()."</span>";
            }
            if (is_page()) {
                $currentPageId = apply_filters('wpml_object_id', get_the_ID(), 'page');
                $ancestors = get_post_ancestors($currentPageId);
                if ($ancestors) {
                    $reverse = array_reverse($ancestors);
                    foreach ($reverse as $ancestor) {
                        $translatedAncestorId = apply_filters('wpml_object_id', $ancestor, 'page');
                        echo "<a href='".get_permalink($translatedAncestorId)."' class='breadcrumbs-item'>".get_the_title($translatedAncestorId)."</a>";
                        echo "<span class='separator'>$separatorChar</span>";
                    }
                }
                echo "<span class='breadcrumbs-item'>".get_the_title($currentPageId)."</span>";
            }
        echo "</div>";
    }

    public function registerImageSizes()
    {
        //tmp image sizes
        add_image_size('full-width', 1920, 900, true);
        add_image_size('banner', 1680, 788, true);
        add_image_size('news_list', 600, 330, true);
        add_image_size('uncropped_list', 600, 330, false);
        add_image_size('single_news', 930, 515, true);
        add_image_size('930_360', 930, 360, true);
        add_image_size('square', 640, 640, true);
    }

    public function addImageSizesToAdmin($sizes)
    {
        return array_merge(
            $sizes,
            [
                'full-width' => __('Full width', self::TEXTDOMAIN),
                'news_list' => __('News list', self::TEXTDOMAIN),
                'single_news' => __('News single', self::TEXTDOMAIN),
                'banner' => __('Banner', self::TEXTDOMAIN),
                'square' => __('Square', self::TEXTDOMAIN),
                '930_360' => __('930 x 360', self::TEXTDOMAIN),
            ]
        );
    }

    public function registerMenus()
    {
        register_nav_menus(
            [
                'main-menu' => __('Main menu', self::TEXTDOMAIN),
                'secondary-menu' => __('Secondary menu', self::TEXTDOMAIN)
            ]
        );
    }

    public function registerSidebars()
    {
        register_sidebar(
            [
                'name' => __('Footer Column 1 (Contact)', self::TEXTDOMAIN),
                'id' => 'footer-column-1',
                'before_widget' => '<section class="widget %1$s %2$s">',
                'after_widget' => '</section>',
                'before_title' => '<h2 class="widget-title">',
                'after_title' => '</h2>'
            ]
        );
        register_sidebar(
            [
                'name' => __('Footer Column 2 (Information 1)', self::TEXTDOMAIN),
                'id' => 'footer-column-2',
                'before_widget' => '<section class="widget %1$s %2$s">',
                'after_widget' => '</section>',
                'before_title' => '<h2 class="widget-title">',
                'after_title' => '</h2>'
            ]
        );
        register_sidebar(
            [
                'name' => __('Footer Column 3 (Information 2)', self::TEXTDOMAIN),
                'id' => 'footer-column-3',
                'before_widget' => '<section class="widget %1$s %2$s">',
                'after_widget' => '</section>',
                'before_title' => '<h2 class="widget-title">',
                'after_title' => '</h2>'
            ]
        );
        register_sidebar(
            [
                'name' => __('Footer Column 3 (Information 3)', self::TEXTDOMAIN),
                'id' => 'footer-column-4',
                'before_widget' => '<section class="widget %1$s %2$s">',
                'after_widget' => '</section>',
                'before_title' => '<h2 class="widget-title">',
                'after_title' => '</h2>'
            ]
        );
        register_sidebar(
            [
                'name' => __('Footer Column 3 (Information 4)', self::TEXTDOMAIN),
                'id' => 'footer-column-5',
                'before_widget' => '<section class="widget %1$s %2$s">',
                'after_widget' => '</section>',
                'before_title' => '<h2 class="widget-title">',
                'after_title' => '</h2>'
            ]
        );
    }

    public function scriptsAndStyles()
    {
        wp_dequeue_style('font-awesome-css');
        wp_deregister_style('font-awesome-css');
        
        // Main stylesheet
        wp_enqueue_style('swedenbio-normalize', get_template_directory_uri().'/assets/css/normalize.css');
        wp_enqueue_style('swedenbio-style', get_stylesheet_uri(), ["swedenbio-normalize"], '1.3.0');
        wp_enqueue_style('swedenbio-buttons', get_template_directory_uri().'/additional.css', ["swedenbio-style"], '1.3.0');
        // wp_enqueue_style('swedenbio-buttons', get_template_directory_uri().'/assets/css/buttons.css', ["swedenbio-style"], '1.3.0');
        wp_enqueue_style('swedenbio-global-red-block', get_template_directory_uri().'/templates/blocks/global-red-box.css', ["swedenbio-style"], '1.3.0');
        wp_enqueue_style('swedenbio-global-calendar-block', get_template_directory_uri().'/templates/blocks/global-calendar-box.css', ["swedenbio-style"], '1.3.0');

        wp_enqueue_script('font-awesome-script', '//use.fontawesome.com/releases/v5.0.6/js/all.js');
        wp_enqueue_script('font-awesome-compatibility', '//use.fontawesome.com/releases/v5.0.6/js/v4-shims.js');
        
        wp_enqueue_script('swedenbio-script', get_template_directory_uri() . '/assets/js/master.js', ['jquery'], '1.3.0', true);
    }

    public function adminScripts($hook)
    {
        wp_enqueue_script('sb-admin_scripts', get_stylesheet_directory_uri() . '/assets/js/adminScripts.js', ['jquery'], '1.0.0', true);
    }


    public function themeSupports()
    {
        // Adds core WordPress HTML5 support.
        add_theme_support('html5', ['caption', 'comment-form', 'comment-list', 'gallery', 'search-form']);
        add_theme_support('post-thumbnails');
        add_theme_support( 'align-wide' );

        // Translation
        load_theme_textdomain('swedenbio', get_template_directory().'/assets/lang');
    }

    /**
    * Remove 'width' and 'height' attributes in images
    */
    public function removeThumbnailDimensions($html)
    {
        if (class_exists('\WooCommerce') && is_woocommerce()) {
            return $html;
        }
        return preg_replace('/(width|height)="\d*?"\s/', '', $html);
    }
      
    /**
    * Clean up WP header
    * Originally from http://wpengineer.com/1438/wordpress-header/
    */
    public function cleanupWP()
    {
        remove_action('wp_head', 'feed_links', 2);
        remove_action('wp_head', 'feed_links_extra', 3);
        remove_action('wp_head', 'rsd_link');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);
        remove_action('wp_head', 'wp_generator');
        remove_action('wp_head', 'wp_shortlink_wp_head', 10, 0);
        remove_action('wp_head', 'rel_canonical');
    }

    public function newsletterForm($atts) {
        $output = '<form class="apsis-subscribe-form widget" name="SubscriberForm" action="https://www.anpdm.com/public/process-subscription-form.aspx?formId=41425F4B76494B5E427440" onSubmit="return MailingListValidation(this);" method="post" target="_blank">';

        if($atts['title']) {
            $output.= '<h2 class="widget-title">'.$atts['title'].'</h2>';
        }

        $output.= '<div>
                        <input type="text" name="pf_Email" value="" size="30" class="apsis-subscribe-input" placeholder="' . __('Your e-mail address', self::TEXTDOMAIN) . '" required>
                        <button role="submit" name="Submit" class="apsis-subscribe-submit btn small" value="' . __('Subscribe', self::TEXTDOMAIN) . '">' . __('Subscribe', self::TEXTDOMAIN) . '</button>
                    </div>
                    <input type="hidden" name="pf_FormType" value="OptInForm">
                    <input type="hidden" name="pf_OptInMethod" value="DoubleOptInMethod">
                    <input type="hidden" name="pf_CounterDemogrFields" value="0">
                    <input type="hidden" name="pf_CounterMailinglists" value="3">
                    <input type="hidden" name="pf_AccountId" value="7249">
                    <input type="hidden" name="pf_ListById" value="1">
                    <input type="hidden" name="pf_Version" value="2">

                    <input type="hidden" name="pf_MailinglistName1" value="894911">
                    <input type="hidden" name="pf_MailinglistName2" value="1649093">
                    <input type="hidden" name="pf_MailinglistName3" value="1079120">
                </form>';
        return $output;
    }
}
