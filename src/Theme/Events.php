<?php


namespace SwedenBio\Theme;


class Events
{
    const CPT = "sb_events";
    const TAXONOMY = "sb_event-category";
    const TEXTDOMAIN = "swedenbio";

    public function __construct()
    {
        add_action('init', [$this, 'addCPTandTaxonomy']);
        add_filter('sb_article-cta-location', [$this, 'addToACFLocation']);
        add_filter('sb_intro-location', [$this, 'addToACFLocation']);
        add_filter('sb_article-cta-posttypes', [$this, 'addToACFPostObject']);
        add_filter('sb_header-buttons-posttypes', [$this, 'addToACFPostObject']);
        add_action('acf/init', [$this, 'addCustomFields']);
        add_action('admin_enqueue_scripts', [$this, 'adminScripts']);

        if (class_exists('ACF') && function_exists('acf_register_block_type')) {
            add_action('acf/init', [$this, 'register_block_calendar_agenda']);
            add_action('acf/init', [$this, 'add_block_fields_calendar_agenda']);

            add_action('acf/init', [$this, 'register_block_calendar_incoming_events']);
        }
    }

    public function adminScripts($hook)
    {
        global $post_type;
        if (is_admin() && $post_type == self::CPT && ($hook === 'post.php' || $hook === 'post-new.php')) {
            wp_enqueue_script('sb-events_datepicker', get_stylesheet_directory_uri() . '/assets/js/restrictDates.js', ['jquery'], '1.0.0', true);
        }
    }

    public function addToACFLocation($array)
    {
        $array[][] = [
            'param' => 'post_type',
            'operator' => '==',
            'value' => self::CPT,
        ];

        return $array;
    }

    public function addToACFPostObject($array)
    {
        $array[] = self::CPT;

        return $array;
    }

    public function addCPTandTaxonomy()
    {

        register_taxonomy(
            self::TAXONOMY,
            [self::CPT],
            [
                'hierarchical'      => true,
                'labels'            => [
                    'name'              => __( 'Event category', self::TEXTDOMAIN ),
                    'singular_name'     => __( 'Event category', self::TEXTDOMAIN ),
                    'menu_name'         => __( 'Event category', self::TEXTDOMAIN ),
                ],
                'show_ui'           => true,
                'show_admin_column' => true,
                'query_var'         => true,
                'meta_box_cb' => true,
                'show_in_rest'               => true,
                'rewrite'           => ['slug' => 'kalenderkategori', 'with_front' => false],
            ]
        );

        register_post_type(
            self::CPT,
            [
                'labels' => [
                    'name'          => __('Calendar', self::TEXTDOMAIN),
                    'singular_name' => __('Calendar', self::TEXTDOMAIN),
                ],
                'public'      => true,
                'has_archive' => false,
                'rewrite'     => ['slug' => 'kalendarium'],
                'taxonomies' => [self::TAXONOMY],
                'show_in_rest' => true,
                'supports' => ['title', 'editor', 'comments', 'revisions', 'trackbacks',  'excerpt', 'page-attributes', 'thumbnail', 'custom-fields', 'author'],
                'menu_position' => 6,
                'menu_icon' => 'dashicons-calendar-alt',
            ]
        );
    }

    public function addCustomFields()
    {
        acf_add_local_field_group([
            'key' => 'group_EVENT_OPTIONS',
            'title' => __('Event options', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_sb-event_start-date',
                    'label' => __('Start date', self::TEXTDOMAIN),
                    'name' => 'sb-event_start_date',
                    'type' => 'date_picker',
                    'display_format' => 'Y-m-d',
                    'return_format' => 'Y-m-d',
                    'first_day' => 1,
                ],
                [
                    'key' => 'field_sb-event_end_date',
                    'label' => __('End date', self::TEXTDOMAIN),
                    'name' => 'sb-event_end_date',
                    'type' => 'date_picker',
                    'display_format' => 'Y-m-d',
                    'return_format' => 'Y-m-d',
                    'first_day' => 1,
                ],
                [
                    'key' => 'field_sb-event_start-time',
                    'label' => __('Start time', self::TEXTDOMAIN),
                    'name' => 'sb-event_start_time',
                    'type' => 'text',
//                    'display_format' => 'H:i',
//                    'return_format' => 'H:i',
                ],
                [
                    'key' => 'field_sb-event_end_time',
                    'label' => __('End time', self::TEXTDOMAIN),
                    'name' => 'sb-event_end_time',
                    'type' => 'text',
//                    'display_format' => 'H:i',
//                    'return_format' => 'H:i',
                ],
                [
                    'key' => 'field_sb-event_place',
                    'label' => __('Place', self::TEXTDOMAIN),
                    'name' => 'sb-event_place',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_sb-event_exclusive',
                    'label' => __('Is exclusive to members?', self::TEXTDOMAIN),
                    'name' => 'sb-event_exclusive',
                    'type' => 'true_false',
                    'ui' => 1
                ],
                [
                    'key' => 'field_sb-event_event_type',
                    'label' => __('Event type', self::TEXTDOMAIN),
                    'name' => 'sb-event_event_type',
                    'type' => 'select',
                    'choices' => [
                        'own' => 'SwedenBio',
                        'partner' => 'Partner',
                        'external' => 'External',
                    ],
                    'default_value' => [
                    ],
                    'allow_null' => 1,
                    'multiple' => 0,
                    'ui' => 1,
                    'ajax' => 0,
                    'return_format' => 'value',
                    'placeholder' => '',
                ],
                [
                    'key' => 'field_sb-event_discount',
                    'label' => __('Member discount?', self::TEXTDOMAIN),
                    'name' => 'sb-event_discount',
                    'type' => 'true_false',
                    'ui' => 1
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => self::CPT,
                    ],
                ],
            ],
            'menu_order' => -10,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => '',
        ]);
    }

    public function register_block_calendar_incoming_events() {
        acf_register_block_type([
            'name'              => 'vm-block-calendar-incoming-events',
            'title'             => __('Calendar: incoming events - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('3 nearest incoming events for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/global-calendar-box.css',
            'align' => 'full',
            'render_callback'   => function(){ echo do_shortcode('[sb_global_calendar_box]'); },
            'category'          => 'widgets',
            'icon'              => 'calendar-alt',
            'keywords'          => ['calendar', 'latest'],
            'supports'          => [
                'align' => ['full'],
            ],
        ]);
    }

    public function register_block_calendar_agenda() {

        acf_register_block_type([
            'name'              => 'vm-block-calendar-agenda',
            'title'             => __('Calendar agenda - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Calendar agenda for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                        'vm_agenda-title' => 'Lorem ipsum dolor sit amet',
                    ],
                ],
            ],
            'align' => 'full',
            'render_template'   => 'templates/blocks/calendar-agenda/calendar-agenda.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/calendar-agenda/calendar-agenda.css',
            'enqueue_script' => get_template_directory_uri() . '/templates/blocks/calendar-agenda/calendar-agenda.js',
            'category'          => 'widgets',
            'icon'              => 'calendar-alt',
            'keywords'          => ['calendar', 'events', 'agenda'],
            'supports'          => [
                'align' => ['full', 'wide'],
            ],
        ]);

    }

    public function add_block_fields_calendar_agenda() {

        acf_add_local_field_group(
            [
                'key' => 'group_vm_calendar-agenda',
                'title' => __('Calendar agenda', self::TEXTDOMAIN),
                'fields' =>
                    [
                        [
                            'key' => 'field_vm_agenda-title',
                            'label' => __('Title', self::TEXTDOMAIN),
                            'name' => 'vm_agenda-title',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_agenda-events',
                            'label' => __('Select events to show', self::TEXTDOMAIN),
                            'name' => 'vm_agenda-events',
                            'type' => 'post_object',
                            'post_type' => [
                                0 => self::CPT,
                            ],
                            'taxonomy' => '',
                            'allow_null' => 1,
                            'multiple' => 1,
                            'return_format' => 'id',
                            'ui' => 1,
                        ],
                    ],
                'location' =>
                    [
                        [
                            [
                                'param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-calendar-agenda',
                            ],
                        ],
                    ],
            ]
        );

    }
}