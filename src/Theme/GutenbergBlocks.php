<?php
namespace SwedenBio\Theme;

class GutenbergBlocks
{

    const TEXTDOMAIN = 'swedenbio';

    public function __construct()
    {
        if (class_exists('ACF') && function_exists('acf_register_block_type')) {

            add_action('enqueue_block_assets', [$this, 'vm_block_styles']);

            add_action('acf/init', [$this, 'register_block_underlined_text']);
            add_action('acf/init', [$this, 'add_block_fields_underlined_text']);

            add_action('acf/init', [$this, 'register_block_button']);
            add_action('acf/init', [$this, 'add_block_fields_button']);

            add_action('acf/init', [$this, 'register_block_pink_static_3_columns_content_box']);
            add_action('acf/init', [$this, 'register_block_static_gray_box']);

            add_action('acf/init', [$this, 'register_block_editable_3_columns']);
            add_action('acf/init', [$this, 'add_block_fields_editable_3_columns']);

            add_action('acf/init', [$this, 'register_block_news']);
            add_action('acf/init', [$this, 'add_block_fields_news']);

            add_action('acf/init', [$this, 'register_block_news_category']);
            add_action('acf/init', [$this, 'add_block_fields_news_category']);

            add_action('acf/init', [$this, 'register_block_raised_text']);
            add_action('acf/init', [$this, 'add_block_fields_raised_text']);

            add_action('acf/init', [$this, 'register_block_timeline']);
            add_action('acf/init', [$this, 'add_block_fields_timeline']);

            add_action('acf/init', [$this, 'register_block_four_images']);
            add_action('acf/init', [$this, 'add_block_fields_four_images']);
        }
        add_action('init', [$this, 'addStylesToBlock']);
        add_action( 'enqueue_block_editor_assets', [$this, 'removeBlocks'] );
        add_action( 'after_setup_theme', [$this, 'addCustomColors'] );
        add_action( 'after_setup_theme', [$this, 'overwrite_rss_block'] );

    }

    public function addCustomColors()
    {
        add_theme_support(
            'editor-color-palette',
            [
                [
                    'name'  => __( 'Red', self::TEXTDOMAIN ),
                    'slug'  => 'red',
                    'color' => '#C51162',
                ],
                [
                    'name'  => __( 'Dark Red', self::TEXTDOMAIN ),
                    'slug'  => 'dark-red',
                    'color' => '#8E0038',
                ],
                [
                    'name'  => __( 'Dark Blue', self::TEXTDOMAIN ),
                    'slug'  => 'dark-blue',
                    'color' => '#002872',
                ],
                [
                    'name'  => __( 'Blue', self::TEXTDOMAIN ),
                    'slug'  => 'blue',
                    'color' => '#214CAC',
                ],
                [
                    'name'  => __( 'Gray', self::TEXTDOMAIN ),
                    'slug'  => 'gray',
                    'color' => '#E5ECF2',
                ],
                [
                    'name'  => __( 'Dark Gray', self::TEXTDOMAIN ),
                    'slug'  => 'dark-gray',
                    'color' => '#94A0AF',
                ],
            ]
        );
    }

    public function removeBlocks()
    {
        wp_enqueue_script(
            'sb-deny-list-blocks',
            get_template_directory_uri() . '/templates/blocks/vm-disable-blocks.js',
            ['wp-blocks', 'wp-dom-ready', 'wp-edit-post']
        );
    }

    public function addStylesToBlock()
    {
        register_block_style(
            'core/media-text',
            [
                'name'         => 'with-paddings',
                'label'        => __( 'With paddings', self::TEXTDOMAIN ),
            ]
        );

        register_block_style(
            'core/gallery',
            [
                'name'         => 'without-margins',
                'label'        => __( 'Without margins', self::TEXTDOMAIN ),
            ]
        );

        register_block_style(
            'core/gallery',
            [
                'name'         => 'max-width',
                'label'        => __( 'Max width', self::TEXTDOMAIN ),
            ]
        );

        register_block_style(
            'core/gallery',
            [
                'name'         => 'max-width-and-no-margins',
                'label'        => __( 'Max width and no margins', self::TEXTDOMAIN ),
            ]
        );
    }

    public function vm_block_styles(){
        wp_enqueue_style( 'vm-blocks', get_template_directory_uri() . '/templates/blocks/vm-blocks-style.css' );
        wp_enqueue_style( 'vm-blocks-general', get_template_directory_uri() . '/templates/blocks/general-block-styles.css' );
        wp_enqueue_style( 'sb_buttons-styles', get_template_directory_uri() . '/assets/css/buttons.css' );
    }

    public static function getBlocksHTMLID(array $block){

        if( !empty($block['anchor']) ) {
            return $block['anchor'];
        }else{
            $block_slug = substr(strstr($block['name'], '/'), 1);
            return $block_slug . '-' . $block['id'];
        }

    }

    public static function getBlocksCSSClasses(array $block){

        $block_slug = substr(strstr($block['name'], '/'), 1);
        $cssClasses = 'vm-block ' . $block_slug;
        if( !empty($block['className']) ) {
            $cssClasses .= ' ' . $block['className'];
        }
        if( !empty($block['align']) ) {
            $cssClasses .= ' align' . $block['align'];
        }
        return $cssClasses;

    }



    public function register_block_underlined_text() {

        acf_register_block_type([
            'name'              => 'vm-block-underlined-text',
            'title'             => __('Underlined text - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Underlined text for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                                'is_preview' => true,
                                'vm_underlined_text' => 'Lorem ipsum dolor sit amet',
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/underlined-text/underlined-text.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/underlined-text/underlined-text.css',
            'category'          => 'text',
            'icon'              => 'editor-underline',
            'keywords'          => ['underline', 'border', 'bottom'],
            'supports'          => [
                'align' => ['wide', 'full'],
            ],
        ]);

    }

    public function add_block_fields_underlined_text() {

        acf_add_local_field_group(
            [
                'key' => 'group_vm_underlined_text',
                'title' => __('Underlined text', self::TEXTDOMAIN),
                'fields' =>
                    [
                        [
                            'key' => 'field_vm_underlined_text',
                            'label' => __('Text to underline', self::TEXTDOMAIN),
                            'name' => 'vm_underlined_text',
                            'type' => 'text',
                        ],
                    ],
                'location' =>
                    [
                        [
                            ['param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-underlined-text',],
                        ],
                    ],
            ]
        );

    }

    public function register_block_button() {

        acf_register_block_type([
            'name'              => 'vm-block-button',
            'title'             => __('Button - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Button for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/button/button.php',
            'category'          => 'text',
            'icon'              => 'button',
            'keywords'          => ['button', 'cta'],
            'supports'          => [
                'align' => false,
            ],
        ]);

    }

    public function add_block_fields_button() {

        acf_add_local_field_group(
            [
                'key' => 'group_vm_button',
                'title' => __('Button', self::TEXTDOMAIN),
                'fields' =>
                    [
                        [
                            'key' => 'field_vm_button_title',
                            'label' => __('Button Title', self::TEXTDOMAIN),
                            'name' => 'vm_button_title',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_button_url',
                            'label' => __('Button URL', self::TEXTDOMAIN),
                            'name' => 'vm_button_url',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_button_open_in_new',
                            'label' => __('Button: Open in new tab', self::TEXTDOMAIN),
                            'name' => 'vm_button_open_in_new',
                            'type' => 'true_false',
                        ],
                        [
                            'key' => 'field_vm_button_center',
                            'label' => __('Center button', self::TEXTDOMAIN),
                            'name' => 'vm_button_center',
                            'type' => 'true_false',
                        ],
                        [
                            'key' => 'field_vm_button_small',
                            'label' => __('Button: Reduce height', self::TEXTDOMAIN),
                            'name' => 'vm_button_small',
                            'type' => 'true_false',
                        ],
                        [
                            'key' => 'field_vm_button_style',
                            'label' => __('Button Style', self::TEXTDOMAIN),
                            'name' => 'vm_button_style',
                            'type' => 'select',
                            'choices' => [
                                'default' => __('Default', self::TEXTDOMAIN),
                                'blue' => __('Blue', self::TEXTDOMAIN),
                                'blue-outline' => __('Blue outline', self::TEXTDOMAIN),
                                'transparent-blue' => __('Transparent blue (invisible on white background)', self::TEXTDOMAIN),
                                'white' => __('White', self::TEXTDOMAIN),
                                'transparent-red' => __('Transparent red (invisible on white background)', self::TEXTDOMAIN),
                                'transparent-gray' => __('Transparent grey', self::TEXTDOMAIN),
                            ],
                            'default_value' => [
                                0 => 'default',
                            ],
                            'allow_null' => 0,
                            'multiple' => 0,
                            'ui' => 1,
                            'ajax' => 0,
                            'return_format' => 'value',
                            'placeholder' => '',
                        ]
                    ],
                'location' =>
                    [
                        [
                            [
                                'param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-button',
                            ],
                        ],
                        [
                            [
                                'param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-news',
                            ],
                        ],
                        [
                            [
                                'param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-raised-text',
                            ],
                        ],
                        [
                            [
                                'param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-news-category',
                            ],
                        ],
                    ],
            ]
        );

    }

    public function register_block_pink_static_3_columns_content_box() {

        acf_register_block_type([
            'name'              => 'vm-block-pink-static-box',
            'title'             => __('Pink static 3 columns content box - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Static content box with 3 columns on a pink background for SwedenBio, same as in the footer of some pages', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'align' => 'full',
            'render_callback'   => function(){ echo do_shortcode('[sb_global_red_box]'); },
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/global-red-box.css',
            'category'          => 'widgets',
            'icon'              => 'text',
            'keywords'          => ['red', 'footer'],
            'supports'          => [
                'align' => ['full'],
            ],
        ]);

    }

    public function register_block_static_gray_box() {

        acf_register_block_type([
            'name'              => 'vm-block-static-gray-box',
            'title'             => __('Gray static 3 columns content box / fact box - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Gray static 3 columns content box / fact box for SwedenBio, same as in the footer of some pages', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'align' => 'full',
            'render_callback'   => function(){ echo do_shortcode('[sb_global_gray_box]'); },
            'category'          => 'widgets',
            'icon'              => 'text',
            'keywords'          => ['factbox', 'footer', 'gray'],
            'supports'          => [
                'align' => ['full'],
            ],
        ]);

    }

    public function register_block_editable_3_columns() {

        acf_register_block_type([
            'name'              => 'vm-block-editable-3-columns',
            'title'             => __('Editable 3 columns - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Editable 3 columns for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/editable_3_columns/editable_3_columns.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/editable_3_columns/editable_3_columns.css',
            'category'          => 'widgets',
            'icon'              => 'text',
            'keywords'          => ['columns', 'background'],
            'supports'          => [
                'align' => false,
            ],
        ]);

    }

    public function add_block_fields_editable_3_columns() {

        acf_add_local_field_group(
            [
                'key' => 'group_vm_editable_3_columns',
                'title' => __('Button', self::TEXTDOMAIN),
                'fields' =>
                    [
                        [
                            'key' => 'field_vm_button_title',
                            'label' => __('Button Title', self::TEXTDOMAIN),
                            'name' => 'vm_button_title',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_button_url',
                            'label' => __('Button URL', self::TEXTDOMAIN),
                            'name' => 'vm_button_url',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_button_open_in_new',
                            'label' => __('Button: Open in new tab', self::TEXTDOMAIN),
                            'name' => 'vm_button_open_in_new',
                            'type' => 'true_false',
                        ],
                        [
                            'key' => 'field_vm_button_small',
                            'label' => __('Button: Reduce height', self::TEXTDOMAIN),
                            'name' => 'vm_button_small',
                            'type' => 'true_false',
                        ],
                        [
                            'key' => 'field_vm_button_style',
                            'label' => __('Button Style', self::TEXTDOMAIN),
                            'name' => 'vm_button_style',
                            'type' => 'select',
                            'choices' => [
                                'default' => __('Default', self::TEXTDOMAIN),
                                'blue' => __('Blue', self::TEXTDOMAIN),
                                'blue-outline' => __('Blue outline', self::TEXTDOMAIN),
                                'transparent-blue' => __('Transparent blue (invisible on white background)', self::TEXTDOMAIN),
                                'white' => __('White', self::TEXTDOMAIN),
                                'transparent-red' => __('Transparent red (invisible on white background)', self::TEXTDOMAIN),
                                'transparent-gray' => __('Transparent grey', self::TEXTDOMAIN),
                            ],
                            'default_value' => [
                                0 => 'default',
                            ],
                            'allow_null' => 0,
                            'multiple' => 0,
                            'ui' => 1,
                            'ajax' => 0,
                            'return_format' => 'value',
                            'placeholder' => '',
                        ]
                    ],
                'location' =>
                    [
                        [
                            ['param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-editable-3-columns',],
                        ],
                    ],
            ]
        );


        acf_add_local_field_group([
            'key' => 'group_editable_3_columns-content',
            'title' => '',
            'fields' => [
                [
                    'key' => 'field_editable_3_columns-title',
                    'label' => __('Title', self::TEXTDOMAIN),
                    'name' => 'editable_3_columns-title',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_editable_3_columns-bg-image',
                    'label' => __('Background image', self::TEXTDOMAIN),
                    'name' => 'editable_3_columns-bg-image',
                    'type' => 'image',
                    'return_format' => 'url',
                    'preview_size' => 'medium',
                ],
                [
                    'key' => 'field_editable_3_columns-bg-color',
                    'label' => __('Background color', self::TEXTDOMAIN),
                    'name' => 'editable_3_columns-bg-color',
                    'type' => 'color_picker',
                ],
                [
                    'key' => 'field_editable_3_columns-bg-color-opacity',
                    'label' => __('Background color opacity', self::TEXTDOMAIN),
                    'name' => 'editable_3_columns-bg-color-opacity',
                    'type' => 'range',
                    'default_value' => 1,
                    'min' => 0,
                    'max' => 1,
                    'step' => 0.1,
                ],
                [
                    'key' => 'field_editable_3_columns-text-color',
                    'label' => __('Text color', self::TEXTDOMAIN),
                    'name' => 'editable_3_columns-text-color',
                    'type' => 'color_picker',
                ],
                [
                    'key' => 'field_editable_3_columns-content',
                    'label' => __('Content', self::TEXTDOMAIN),
                    'name' => 'editable_3_columns-content',
                    'type' => 'repeater',
                    'min' => 3,
                    'max' => 3,
                    'layout' => 'block',
                    'sub_fields' => [
                        [
                            'key' => 'field_editable_3_columns-image',
                            'label' => __('Image', self::TEXTDOMAIN),
                            'name' => 'editable_3_columns-image',
                            'type' => 'image',
                            'return_format' => 'id',
                            'preview_size' => 'medium',
                            'library' => 'all'
                        ],
                        [
                            'key' => 'field_editable_3_columns-text',
                            'label' => __('Text', self::TEXTDOMAIN),
                            'name' => 'editable_3_columns-text',
                            'type' => 'wysiwyg',
                            'tabs' => 'all',
                            'toolbar' => 'full',
                            'media_upload' => 0,
                            'delay' => 1,
                        ],
                    ],
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/vm-block-editable-3-columns'
                    ],
                ],
            ],
        ]);
    }

    public function register_block_news() {

        acf_register_block_type([
            'name'              => 'vm-block-news',
            'title'             => __('News - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('News for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/news/news.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/news/news.css',
            'category'          => 'widgets',
            'icon'              => 'text',
            'keywords'          => ['list', 'news'],
            'supports'          => [
                'align' => false,
            ],
        ]);

    }

    public function add_block_fields_news()
    {
        acf_add_local_field_group([
            'key' => 'group_vm-news',
            'title' => '',
            'fields' => [
                [
                    'key' => 'field_vm-news-title',
                    'label' => __('Title', self::TEXTDOMAIN),
                    'name' => 'vm-news-title',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_vm-news-number',
                    'label' => __('Number of posts to show', self::TEXTDOMAIN),
                    'name' => 'vm-news-number',
                    'type' => 'number',
                ],
                [
                    'key' => 'field_vm-news-columns',
                    'label' => __('Number of columns', self::TEXTDOMAIN),
                    'name' => 'vm-news-columns',
                    'type' => 'select',
                    'choices' => [
                        '2' => '2',
                        '3' => '3',
                    ],
                    'default_value' => [
                        0 => '2',
                    ],
                    'allow_null' => 0,
                    'multiple' => 0,
                    'ui' => 1,
                    'ajax' => 0,
                    'return_format' => 'value',
                    'placeholder' => '',
                ],
                [
                    'key' => 'field_vm-sticky-posts',
                    'label' => __("Sticky posts", self::TEXTDOMAIN),
                    'name' => 'vm-sticky-posts',
                    'type' => 'post_object',
                    'post_type' => [
                        0 => 'post',
                    ],
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/vm-block-news'
                    ],
                ],
            ],
        ]);
    }

    public function register_block_news_category() {

        acf_register_block_type([
            'name'              => 'vm-block-news-category',
            'title'             => __('News Category - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('News Category for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'align' => 'full',
            'render_template'   => 'templates/blocks/news_category/news_category.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/news_category/news_category.css',
            'category'          => 'widgets',
            'icon'              => 'text',
            'keywords'          => ['list', 'news'],
            'supports'          => [
                'align' => ['full'],
            ],
        ]);

    }

    public function add_block_fields_news_category()
    {
        acf_add_local_field_group([
            'key' => 'group_vm-news-category',
            'title' => '',
            'fields' => [
                [
                    'key' => 'field_vm-news-category-number',
                    'label' => __('Number of posts to show', self::TEXTDOMAIN),
                    'name' => 'vm-news-category-number',
                    'type' => 'number',
                    'default_value' => 3
                ],
                [
                    'key' => 'field_vm-news-category',
                    'label' => __('Category', self::TEXTDOMAIN),
                    'name' => 'vm-news-category',
                    'type' => 'taxonomy',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'taxonomy' => 'category',
                    'field_type' => 'select',
                    'allow_null' => 0,
                    'add_term' => 0,
                    'save_terms' => 0,
                    'load_terms' => 0,
                    'return_format' => 'id',
                    'multiple' => 0,
                ],
                [
                    'key' => 'field_vm-sticky-posts',
                    'label' => __("Sticky posts", self::TEXTDOMAIN),
                    'name' => 'vm-sticky-posts',
                    'type' => 'post_object',
                    'post_type' => [
                        0 => 'post',
                    ],
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/vm-block-news-category'
                    ],
                ],
            ],
        ]);
    }


    public function register_block_raised_text() {

        acf_register_block_type([
            'name'              => 'vm-block-raised-text',
            'title'             => __('Raised Text - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Raised Text for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/raised_text/raised_text.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/raised_text/raised_text.css',
            'category'          => 'widgets',
            'icon'              => 'cover-image',
            'keywords'          => ['text', 'link'],
            'supports'          => [
                'align' => ['full'],
            ],
        ]);

    }

    public function add_block_fields_raised_text()
    {
        acf_add_local_field_group([
            'key' => 'group_raised-text',
            'title' => '',
            'fields' => [
                [
                    'key' => 'field_vm-raised-text-title',
                    'label' => __('Title', self::TEXTDOMAIN),
                    'name' => 'vm-raised-text-title',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_vm-raised-text-content',
                    'label' => __('Text', self::TEXTDOMAIN),
                    'name' => 'vm-raised-text-content',
                    'type' => 'textarea',
                ],
                [
                    'key' => 'field_vm-raised-text-show-icon',
                    'label' => __('Show play icon', self::TEXTDOMAIN),
                    'name' => 'vm-raised-text-show-icon',
                    'type' => 'true_false',
                ],
                [
                    'key' => 'field_vm-raised-text-image',
                    'label' => __('Image', self::TEXTDOMAIN),
                    'name' => 'vm-raised-text-image',
                    'type' => 'image',
                    'return_format' => 'id',
                    'preview_size' => 'medium',
                    'library' => 'all'
                ],
                [
                    'key' => 'field_vm-raised-text-link',
                    'label' => __('Image link', self::TEXTDOMAIN),
                    'name' => 'vm-raised-text-link',
                    'type' => 'text',
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/vm-block-raised-text'
                    ],
                ],
            ],
        ]);
    }


    public function register_block_timeline() {

        acf_register_block_type([
            'name'              => 'vm-block-timeline',
            'title'             => __('Timeline - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Timeline for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/timeline/timeline.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/timeline/timeline.css',
            'category'          => 'widgets',
            'icon'              => 'feedback',
            'keywords'          => ['timeline', 'history'],
            'supports'          => [
                'align' => false,
            ],
        ]);

    }

    public function add_block_fields_timeline()
    {

        acf_add_local_field_group([
            'key' => 'group_5f8827bf3cfd0',
            'title' => '',
            'fields' => [
                [
                    'key' => 'field_vb-timeline_title',
                    'label' => __('Title', self::TEXTDOMAIN),
                    'name' => 'vb-timeline_title',
                    'type' => 'text'
                ],
                [
                    'key' => 'field_vb-timeline_items',
                    'label' => __('Timeline', self::TEXTDOMAIN),
                    'name' => 'vb-timeline_items',
                    'type' => 'repeater',
                    'min' => 0,
                    'max' => 0,
                    'layout' => 'block',
                    'button_label' => '',
                    'sub_fields' => [
                        [
                            'key' => 'field_vb-timeline_year',
                            'label' => __('Year(s)', self::TEXTDOMAIN),
                            'name' => 'vb-timeline_year',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vb-timeline_titles',
                            'label' => __('Title', self::TEXTDOMAIN),
                            'name' => 'vb-timeline_titles',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vb-timeline_content',
                            'label' => __('Content', self::TEXTDOMAIN),
                            'name' => 'vb-timeline_content',
                            'type' => 'wysiwyg',
                            'tabs' => 'visual',
                            'toolbar' => 'full',
                            'media_upload' => 1,
                            'delay' => 1,
                        ]
                    ],
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/vm-block-timeline'
                    ],
                ],
            ],
        ]);
    }

    public function register_block_four_images() {

        acf_register_block_type([
            'name'              => 'vm-block-four-images',
            'title'             => __('4 images - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('4 images for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/four_images/four_images.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/four_images/four_images.css',
            'category'          => 'widgets',
            'icon'              => 'feedback',
            'keywords'          => ['images', 'gallery', '4'],
            'supports'          => [
                'align' => false,
            ],
        ]);

    }

    public function add_block_fields_four_images()
    {

        acf_add_local_field_group([
            'key' => 'group_VB_FOUR_IMAGES',
            'title' => '',
            'fields' => [
                [
                    'key' => 'field_vb-four_images_items',
                    'label' => __('Images', self::TEXTDOMAIN),
                    'name' => 'vb-four_images_items',
                    'type' => 'repeater',
                    'min' => 4,
                    'max' => 4,
                    'layout' => 'block',
                    'button_label' => '',
                    'sub_fields' => [
                        [
                            'key' => 'field_vb-four_images_items-image',
                            'label' => __('Image', self::TEXTDOMAIN),
                            'name' => 'vb-four_images_items-image',
                            'type' => 'image',
                        ]
                    ],
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/vm-block-four-images'
                    ],
                ],
            ],
        ]);
    }


    public function overwrite_rss_block()
    {
        if (!is_admin()) {
            register_block_type( 'core/rss', array(
                'render_callback' => [$this, 'render_rss'],
            ) );
        }
    }

    public function render_rss($attributes)
    {
        if (!isset($attributes['feedURL'])) {
            return;
        }

        $rss = fetch_feed( $attributes['feedURL'] );

        if ( is_wp_error( $rss ) ) {
            return '<div class="components-placeholder"><div class="notice notice-error"><strong>' . __( 'RSS Error:' ) . '</strong> ' . $rss->get_error_message() . '</div></div>';
        }

        if ( ! $rss->get_item_quantity() ) {
            return '<div class="components-placeholder"><div class="notice notice-error">' . __( 'An error has occurred, which probably means the feed is down. Try again later.' ) . '</div></div>';
        }

        $rss_items  = $rss->get_items( 0, $attributes['itemsToShow'] );
        $list_items = '';
        foreach ( $rss_items as $item ) {

            $description = $item->get_description();
            preg_match('/<img.+src=[\'"](?P<src>.+?)[\'"].*>/i', $description, $image);
            $img = sprintf(
                '<div class="wp-block-rss__item-img"><img src="%1$s" /></div> ',
                isset($image['src']) ? $image['src'] : get_stylesheet_directory_uri().'/assets/images/SWEBIO_Icon_Branschnyheter.png'
            );
            $title = esc_html( trim( strip_tags( $item->get_title() ) ) );
            if ( empty( $title ) ) {
                $title = __( '(no title)' );
            }
            $link = $item->get_link();
            $link = esc_url( $link );
            if ( $link ) {
                $title = "<a href='{$link}' target='_blank'>{$title}</a>";
            } else {
                $sourceTags = $item->get_item_tags(NULL, "source");
                $source = $sourceTags[0]['attribs']['']['url'];
                $link = esc_url($source);
                $title = "<a href='{$link}' target='_blank'>{$title}</a>";
            }
            $title = "<div class='wp-block-rss__item-title'>{$title}</div>";

            $date = '';
            if ( isset($attributes['displayDate']) && $attributes['displayDate'] ) {
                $date = $item->get_date( 'U' );

                if ( $date ) {
                    $date = sprintf(
                        '<time datetime="%1$s" class="wp-block-rss__item-publish-date">%2$s</time> ',
                        date_i18n( get_option( 'c' ), $date ),
                        date_i18n( get_option( 'date_format' ), $date )
                    );
                }
            }

            $author = '';
            if ( isset($attributes['displayAuthor']) && $attributes['displayAuthor'] ) {
                $author = $item->get_author();
                $source = $item->get_item_tags('', 'source');
                if ( is_object( $author ) ) {
                    $author = $author->get_name();
                    $author = '<span class="wp-block-rss__item-author">' . sprintf(
                        /* translators: %s: the author. */
                            __( 'by %s' ),
                            esc_html( strip_tags( $author ) )
                        ) . '</span>';
                } else if (isset($source[0]['data'])) {
                    $author = '<span class="wp-block-rss__item-author">' . sprintf(
                        /* translators: %s: the author. */
                            __( 'by %s' ),
                            esc_html( strip_tags( $source[0]['data'] ) )
                        ) . '</span>';
                }
            }

            $excerpt = '';
            if ( isset($attributes['displayExcerpt']) && $attributes['displayExcerpt'] ) {
                $excerpt = html_entity_decode( $item->get_description(), ENT_QUOTES, get_option( 'blog_charset' ) );
                $excerpt = esc_attr( wp_trim_words( $excerpt, $attributes['excerptLength'], ' [&hellip;]' ) );

                // Change existing [...] to [&hellip;].
                if ( '[...]' === substr( $excerpt, -5 ) ) {
                    $excerpt = substr( $excerpt, 0, -5 ) . '[&hellip;]';
                }

                $excerpt = '<div class="wp-block-rss__item-excerpt">' . esc_html( $excerpt ) . '</div>';
            }

            $list_items .= "<li class='wp-block-rss__item'>{$img}<div class='wp-block-rss__item-content'>{$title}{$date}{$author}{$excerpt}</div></li>";
        }

        $class = 'wp-block-rss';
        if ( isset( $attributes['align'] ) ) {
            $class .= ' align' . $attributes['align'];
        }

        if ( isset( $attributes['blockLayout'] ) && 'grid' === $attributes['blockLayout'] ) {
            $class .= ' is-grid';
        }

        if ( isset( $attributes['columns'] ) && 'grid' === $attributes['blockLayout'] ) {
            $class .= ' columns-' . $attributes['columns'];
        }

        if ( isset( $attributes['className'] ) ) {
            $class .= ' ' . $attributes['className'];
        }

        return sprintf( '<ul class="%s">%s</ul>', esc_attr( $class ), $list_items );
    }
}
