<?php


namespace SwedenBio\Theme;


class Members
{
    const CPT = "medlemmar";
    const TAXONOMY = "user_categories";
    const TEXTDOMAIN = "swedenbio";

    public function __construct()
    {
        add_action('init', [$this, 'addCPTandTaxonomy']);
        //add_filter('sb_article-cta-location', [$this, 'addToACFLocation']);
        add_filter('sb_intro-location', [$this, 'addToACFLocation']);
        add_filter('sb_article-cta-posttypes', [$this, 'addToACFPostObject']);
        add_filter('sb_header-buttons-posttypes', [$this, 'addToACFPostObject']);
        add_action('acf/init', [$this, 'addCustomFields']);

        if (class_exists('ACF') && function_exists('acf_register_block_type')) {
            add_action('acf/init', [$this, 'register_block_sb_members']);
            add_action('acf/init', [$this, 'add_block_fields_sb_members']);
        }
    }

    public function addToACFLocation($array)
    {
        $array[][] = [
            'param' => 'post_type',
            'operator' => '==',
            'value' => self::CPT,
        ];

        return $array;
    }

    public function addToACFPostObject($array)
    {
        $array[] = self::CPT;

        return $array;
    }

    public function addCPTandTaxonomy()
    {

        register_taxonomy(
            self::TAXONOMY,
            [self::CPT],
            [
                'hierarchical'      => true,
                'labels'            => [
                    'name'              => __( 'Business categories', self::TEXTDOMAIN ),
                    'singular_name'     => __( 'Business categories', self::TEXTDOMAIN ),
                    'menu_name'         => __( 'Business categories', self::TEXTDOMAIN ),
                ],
                'show_ui'           => true,
                'show_admin_column' => true,
                'query_var'         => true,
                'meta_box_cb' => true,
                'show_in_rest'               => true,
                'rewrite'           => ['slug' => 'foretagskategorier', 'with_front' => false],
            ]
        );

        register_post_type(
            self::CPT,
            [
                'labels' => [
                    'name'          => __('Members', self::TEXTDOMAIN),
                    'singular_name' => __('Members', self::TEXTDOMAIN),
                ],
                'public'      => true,
                'has_archive' => false,
                'rewrite'     => ['slug' => 'medlemmar'],
                'taxonomies' => [self::TAXONOMY],
                'show_in_rest' => true,
                'supports' => ['title', 'editor', 'comments', 'revisions', 'trackbacks',  'excerpt', 'page-attributes', 'thumbnail', 'custom-fields', 'author'],
                'menu_position' => 6,
                'menu_icon' => 'dashicons-businessman',
            ]
        );
    }

    public function addCustomFields()
    {
        acf_add_local_field_group([
            'key' => 'group_MEMBERS_FIELDS',
            'title' => __('Member companies', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_sb-member-website',
                    'label' => __('Web site', self::TEXTDOMAIN),
                    'name' => 'website',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_sb-member-biotechgate_link',
                    'label' => __('Biotech gate link', self::TEXTDOMAIN),
                    'name' => 'biotechgate_link',
                    'type' => 'url',
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => self::CPT,
                    ],
                ],
            ],
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'seamless',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => 1,
            'description' => '',
        ]);
    }


    public function register_block_sb_members() {

        acf_register_block_type([
            'name'              => 'vm-block-members',
            'title'             => __('Members block - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('Members block for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/members-block/members-block.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/members-block/members-block.css',
            'category'          => 'widgets',
            'icon'              => 'businessman',
            'keywords'          => ['members'],
        ]);

    }

    public function add_block_fields_sb_members() {

        acf_add_local_field_group(
            [
                'key' => 'group_vm_members-block',
                'title' => __('Members block', self::TEXTDOMAIN),
                'fields' =>
                    [
                        [
                            'key' => 'field_vm_members-title',
                            'label' => __('Title', self::TEXTDOMAIN),
                            'name' => 'vm_members_block-title',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_members-padding',
                            'label' => __('Add vertical padding', self::TEXTDOMAIN),
                            'name' => 'vm_members_block-padding',
                            'type' => 'true_false',
                            "ui" => 1
                        ],
                        [
                            'key' => 'field_vm_members-description',
                            'label' => __('Description', self::TEXTDOMAIN),
                            'name' => 'vm_members_block-description',
                            'type' => 'textarea',
                        ],
                        [
                            'key' => 'field_vm_members-posts',
                            'label' => __('Select members to show', self::TEXTDOMAIN),
                            'name' => 'vm_members_block-posts',
                            'type' => 'post_object',
                            'post_type' => [
                                0 => self::CPT,
                            ],
                            'taxonomy' => '',
                            'allow_null' => 1,
                            'multiple' => 1,
                            'return_format' => 'id',
                            'ui' => 1,
                        ],
                    ],
                'location' =>
                    [
                        [
                            [
                                'param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-members',
                            ],
                        ],
                    ],
            ]
        );

    }
}