<?php
namespace SwedenBio\Theme;

class ACF
{

    const TEXTDOMAIN = 'swedenbio';

    public function __construct()
    {

        add_action('acf/init', [$this, 'articleFields']);

        if (function_exists('acf_add_options_page')) {
            add_action('acf/init', [$this, 'generalSettings']);
        }
    }

    public function articleFields()
    {
        $introArrayLocation = [
            [
                [
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'post',
                ],
            ],
            [
                [
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'page',
                ],
            ],
        ];
        $INTRO_location = apply_filters('sb_intro-location', $introArrayLocation);
 //INGRESS / INTRO
        acf_add_local_field_group([
            'key' => 'group_INTRO_GROUP',
            'title' => __('Ingress', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_sb_intro',
                    'label' => __('Ingress', self::TEXTDOMAIN),
                    'name' => 'sb_intro',
                    'type' => 'textarea',
                ],
            ],
            'location' => $INTRO_location,
            'menu_order' => 0,
            'position' => 'side',
            'active' => true,
        ]);

        $ctaArrayLocation = [
            [
                [
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'post',
                ],
            ],
        ];


        acf_add_local_field_group([
            'key' => 'group_HIDE_FEATURED_IMAGE',
            'title' => __('Hide Image', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_sb_hide-image',
                    'label' => __('Hide Image', self::TEXTDOMAIN),
                    'name' => 'sb_hide-image',
                    'type' => 'true_false',
                    'ui' => '1'
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'sb_events',
                    ],
                ],
                [
                    [
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'post',
                    ],
                ],
            ],
            'menu_order' => 0,
            'position' => 'side',
            'active' => true,
        ]);

        acf_add_local_field_group([
            'key' => 'group_HIDE_FROM_NEWS_BLOCK',
            'title' => __('Hide from news block', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_sb_hide-from-news-block',
                    'label' => __('Hide', self::TEXTDOMAIN),
                    'name' => 'sb_hide-from-news-block',
                    'type' => 'true_false',
                    'ui' => '1'
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'post',
                    ],
                ],
            ],
            'menu_order' => 0,
            'position' => 'side',
            'active' => true,
        ]);

        $CTA_location = apply_filters('sb_article-cta-location', $ctaArrayLocation);
        $CTA_postobject_types = apply_filters('sb_article-cta-posttypes', [
            0 => 'post',
            1 => 'page',
        ]);

	// COMPANY NAME - adds a field in posts connected to Anslagstavlan
        acf_add_local_field_group([
            'key' => 'group_COMPANY_NAME',
            'title' => __('Company name', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_sb_company-name',
                    'label' => __('Company name', self::TEXTDOMAIN),
                    'name' => 'sb_company-name',
                    'type' => 'text',
                    'ui' => '1'
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'sb_member_posts',
                    ],
                ],
            ],
            'menu_order' => 0,
            'position' => 'normal',
            'active' => true,
        ]);


	// COMPANY NAME - adds a field in posts connected to Member Offers
        acf_add_local_field_group([
            'key' => 'group_COMPANY_NAME_offers',
            'title' => __('Company name', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_sb_company-name_offers',
                    'label' => __('Company name', self::TEXTDOMAIN),
                    'name' => 'sb_company-name_offers',
                    'type' => 'text',
                    'ui' => '1'
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'sb_member_offers',
                    ],
                ],
            ],
            'menu_order' => 0,
            'position' => 'normal',
            'active' => true,
        ]);

        //CTA
        acf_add_local_field_group([
            'key' => 'group_CTA_BUTTONS',
            'title' => __('CTA buttons', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_after_article_cta',
                    'label' => __('Button', self::TEXTDOMAIN),
                    'name' => 'after_article_cta',
                    'type' => 'repeater',
                    'layout' => 'block',
                    'sub_fields' => [
                        [
                            'key' => 'field_after_article_cta-text',
                            'label' => __('Text', self::TEXTDOMAIN),
                            'name' => 'text',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_after_article_cta-is-external',
                            'label' => __('Is external', self::TEXTDOMAIN),
                            'name' => 'is_external',
                            'type' => 'true_false',
                            'default_value' => 0,
                            'ui' => 1,
                        ],
                        [
                            'key' => 'field_after_article_cta-external-url',
                            'label' => __('External url', self::TEXTDOMAIN),
                            'name' => 'external_url',
                            'type' => 'text',
                            'conditional_logic' => [
                                [
                                    [
                                        'field' => 'field_after_article_cta-is-external',
                                        'operator' => '==',
                                        'value' => '1',
                                    ],
                                ],
                            ],
                        ],
                        [
                            'key' => 'field_after_article_cta-internal-url',
                            'label' => __('Internal url', self::TEXTDOMAIN),
                            'name' => 'internal_url',
                            'type' => 'post_object',
                            'conditional_logic' => [
                                [
                                    [
                                        'field' => 'field_after_article_cta-is-external',
                                        'operator' => '!=',
                                        'value' => '1',
                                    ],
                                ],
                            ],
                            'post_type' => $CTA_postobject_types,
                            'allow_null' => 1,
                            'multiple' => 0,
                            'return_format' => 'id',
                            'ui' => 1,
                        ],
                    ],
                ],
            ],
            'location' => $CTA_location,
            'menu_order' => 0,
            'position' => 'normal',
        ]);
    }

    public function generalSettings()
    {
        acf_add_options_page([
            'page_title'    => __('Theme Options', self::TEXTDOMAIN),
            'menu_title'    => __('Theme Options', self::TEXTDOMAIN),
            'menu_slug'     => 'swedenbio-theme-options',
            'capability'    => 'manage_options',
            'redirect'      => false,
            'parent_slug' => 'themes.php',
        ]);

        acf_add_local_field_group([
            'key' => 'group_logo',
            'title' => __('General options', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_header_logo',
                    'label' => __('Logo', self::TEXTDOMAIN),
                    'name' => 'header_logo',
                    'type' => 'image',
                    'return_format' => 'url',
                    'preview_size' => 'medium',
                    'library' => 'all',
                ],
                [
                    'key' => 'field_header_en_page',
                    'label' => __('English page', self::TEXTDOMAIN),
                    'name' => 'en_page',
                    'type' => 'post_object',
                    'post_type' => [
                        0 => 'page'
                    ],
                    'allow_null' => 1,
                    'multiple' => 0,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_sb_favicon',
                    'label' => __('Favicon (must be square, min. 512x512px)', self::TEXTDOMAIN),
                    'name' => 'sb_favicon',
                    'type' => 'image',
                    'return_format' => 'url',
                    'preview_size' => 'thumbnail',
                    'library' => 'all',
                    'min_width' => 512,
                    'min_height' => 512,
                    'max_width' => 1024,
                    'max_height' => 1024,
                    'max_size' => 1,
                    'mime_types' => 'png',
                ],
                [
                    'key' => 'field_newsletter_description',
                    'label' => __('Popup newsletter description', self::TEXTDOMAIN),
                    'name' => 'newsletter_description',
                    'type' => 'textarea'
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'swedenbio-theme-options',
                    ],
                ],
            ],
            'position' => 'normal',
            //'style' => 'seamless',
            'active' => true,
        ]);

        $HEADERBUTTONS_postobject_types = apply_filters('sb_header-buttons-posttypes', [
            0 => 'post',
            1 => 'page',
        ]);
        acf_add_local_field_group([
            'key' => 'group_header_buttons',
            'title' => __('Header buttons', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_header_buttons_repeater',
                    'label' => '',
                    'name' => 'header_buttons_repeater',
                    'type' => 'repeater',
                    'min' => 4,
                    'max' => 4,
                    'layout' => 'table',
                    'sub_fields' => [
                        [
                            'key' => 'field_header_buttons_repeater_text_long',
                            'label' => __('Text long', self::TEXTDOMAIN),
                            'name' => 'text_long',
                            'type' => 'text',
                            'required' => 1,
                        ],
                        [
                            'key' => 'field_header_buttons_repeater_text_short',
                            'label' => __('Text short', self::TEXTDOMAIN),
                            'name' => 'text_short',
                            'type' => 'text',
                            'required' => 1,
                        ],

                        [
                            'key' => 'field_header_buttons_repeater_icon',
                            'label' => __('Icon', self::TEXTDOMAIN),
                            'name' => 'icon',
                            'type' => 'image',
                            'return_format' => 'url',
                            'preview_size' => 'medium',
                            'library' => 'all',
                        ],
                        [
                            'key' => 'field_header_buttons_repeater_url',
                            'label' => __('URL', self::TEXTDOMAIN),
                            'name' => 'url',
                            'type' => 'post_object',
                            'post_type' => $HEADERBUTTONS_postobject_types,
                            'allow_null' => 1,
                            'multiple' => 0,
                            'return_format' => 'id',
                            'ui' => 1,
                        ],
                        [
                            'key' => 'field_header_buttons_only_mobile',
                            'label' => __('Only for mobile', self::TEXTDOMAIN),
                            'name' => 'only_mobile',
                            'type' => 'true_false',
                            'ui' => 1,
                        ],
                    ],
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'swedenbio-theme-options',
                    ],
                ],
            ],
            'position' => 'normal',
            'style' => 'seamless',
            'active' => true,
        ]);

        acf_add_local_field_group([
            'key' => 'group_GLOBAL_RED_BOX',
            'title' => __('Configure global "red" box', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_GLOBAL_RED_BOX-message',
                    'type' => 'message',
                    'message' => 'Shortcode [sb_global_red_box]',
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-title',
                    'label' => __('Title', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-title',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-cta-text',
                    'label' => __('CTA button text', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-cta_button_text',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-cta-url',
                    'label' => __('CTA button url', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-cta_button_url',
                    'type' => 'post_object',
                    'allow_null' => 0,
                    'multiple' => 0,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-content',
                    'label' => __('Content', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-content',
                    'type' => 'repeater',
                    'min' => 3,
                    'max' => 3,
                    'layout' => 'row',
                    'sub_fields' => [
                        [
                            'key' => 'field_GLOBAL_RED_BOX-image',
                            'label' => __('Image', self::TEXTDOMAIN),
                            'name' => 'GLOBAL_RED_BOX-image',
                            'type' => 'image',
                            'return_format' => 'id',
                            'preview_size' => 'medium',
                            'library' => 'all'
                        ],
                        [
                            'key' => 'field_GLOBAL_RED_BOX-text',
                            'label' => __('Text', self::TEXTDOMAIN),
                            'name' => 'GLOBAL_RED_BOX-text',
                            'type' => 'wysiwyg',
                            'tabs' => 'all',
                            'toolbar' => 'full',
                            'media_upload' => 0,
                            'delay' => 1,
                        ],
                    ],
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-attach-to-posts',
                    'label' => __('Attach to posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-attach_to_posts',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-exclude-from-posts',
                    'label' => __('exclude from posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-exclude_from_posts',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_RED_BOX-attach-to-posts',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'post',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-attach-to-archives',
                    'label' => __('Attach to archives (search, categories, list of news etc.)', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-attach_to_archives',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-exclude-categories',
                    'label' => __('exclude from categories', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-exclude_from_categories',
                    'type' => 'taxonomy',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_RED_BOX-attach-to-archives',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'taxonomy' => 'category',
                    'field_type' => 'multi_select',
                    'allow_null' => 1,
                    'add_term' => 0,
                    'save_terms' => 0,
                    'load_terms' => 0,
                    'return_format' => 'id',
                    'multiple' => 0,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-attach-to-calendar',
                    'label' => __('Attach to calendar', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-attach_to_calendar',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-exclude-from-calendar',
                    'label' => __('exclude from events', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-exclude_from_calendar',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_RED_BOX-attach-to-calendar',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'sb_events',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-attach-to-members',
                    'label' => __('Attach to members', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-attach_to_members',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-exclude-from-members',
                    'label' => __('exclude from members', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-exclude_from_members',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_RED_BOX-attach-to-members',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'medlemmar',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-attach-to-members_posts',
                    'label' => __('Attach to members posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-attach_to_members_posts',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_RED_BOX-exclude-from-members_posts',
                    'label' => __('exclude from members posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_RED_BOX-exclude_from_members_posts',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_RED_BOX-attach-to-members_posts',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'sb_member_posts',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'swedenbio-theme-options',
                    ],
                ],
            ],
            'menu_order' => 5,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
        ]);

        //calendar global box
        acf_add_local_field_group([
            'key' => 'group_GLOBAL_CALENDAR_BOX',
            'title' => __('Configure global "calendar" box', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-message',
                    'type' => 'message',
                    'message' => 'Shortcode [sb_global_calendar_box]',
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-title',
                    'label' => __('Title', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-title',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-cta-text',
                    'label' => __('CTA button text', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-cta_button_text',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-attach-to-posts',
                    'label' => __('Attach to posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-attach_to_posts',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-exclude-from-posts',
                    'label' => __('exclude from posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-exclude_from_posts',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_CALENDAR_BOX-attach-to-posts',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'post',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-attach-to-archives',
                    'label' => __('Attach to archives (search, categories, list of news etc.)', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-attach_to_archives',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-exclude-categories',
                    'label' => __('exclude from categories', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-exclude_from_categories',
                    'type' => 'taxonomy',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_CALENDAR_BOX-attach-to-archives',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'taxonomy' => 'category',
                    'field_type' => 'multi_select',
                    'allow_null' => 1,
                    'add_term' => 0,
                    'save_terms' => 0,
                    'load_terms' => 0,
                    'return_format' => 'id',
                    'multiple' => 0,
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-attach-to-calendar',
                    'label' => __('Attach to calendar', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-attach_to_calendar',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-exclude-from-calendar',
                    'label' => __('exclude from events', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-exclude_from_calendar',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_CALENDAR_BOX-attach-to-calendar',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'sb_events',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-attach-to-members',
                    'label' => __('Attach to members', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-attach_to_members',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-exclude-from-members',
                    'label' => __('exclude from members', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-exclude_from_members',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_CALENDAR_BOX-attach-to-members',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'medlemmar',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-attach-to-members_posts',
                    'label' => __('Attach to members posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-attach_to_members_posts',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_CALENDAR_BOX-exclude-from-members_posts',
                    'label' => __('exclude from members posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_CALENDAR_BOX-exclude_from_members_posts',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_CALENDAR_BOX-attach-to-members_posts',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'sb_member_posts',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'swedenbio-theme-options',
                    ],
                ],
            ],
            'menu_order' => 5,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
        ]);


        acf_add_local_field_group([
            'key' => 'group_GLOBAL_GREY_BOX',
            'title' => __('Configure global "gray" / fact box', self::TEXTDOMAIN),
            'fields' => [
                [
                    'key' => 'field_GLOBAL_GREY_BOX-message',
                    'type' => 'message',
                    'message' => 'Shortcode [sb_global_gray_box]',
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-title',
                    'label' => __('Title', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-title',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-cta-text',
                    'label' => __('CTA button text', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-cta_button_text',
                    'type' => 'text',
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-cta-url',
                    'label' => __('CTA button url', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-cta_button_url',
                    'type' => 'post_object',
                    'allow_null' => 0,
                    'multiple' => 0,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-content',
                    'label' => __('Content', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-content',
                    'type' => 'repeater',
                    'min' => 3,
                    'max' => 3,
                    'layout' => 'row',
                    'sub_fields' => [
                        [
                            'key' => 'field_GLOBAL_GREY_BOX-image',
                            'label' => __('Image', self::TEXTDOMAIN),
                            'name' => 'GLOBAL_GREY_BOX-image',
                            'type' => 'image',
                            'return_format' => 'id',
                            'preview_size' => 'medium',
                            'library' => 'all'
                        ],
                        [
                            'key' => 'field_GLOBAL_GREY_BOX-text',
                            'label' => __('Text', self::TEXTDOMAIN),
                            'name' => 'GLOBAL_GREY_BOX-text',
                            'type' => 'wysiwyg',
                            'tabs' => 'all',
                            'toolbar' => 'full',
                            'media_upload' => 0,
                            'delay' => 1,
                        ],
                    ],
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-attach-to-posts',
                    'label' => __('Attach to posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-attach_to_posts',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-exclude-from-posts',
                    'label' => __('exclude from posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-exclude_from_posts',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_GREY_BOX-attach-to-posts',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'post',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-attach-to-archives',
                    'label' => __('Attach to archives (search, categories, list of news etc.)', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-attach_to_archives',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-exclude-categories',
                    'label' => __('exclude from categories', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-exclude_from_categories',
                    'type' => 'taxonomy',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_GREY_BOX-attach-to-archives',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'taxonomy' => 'category',
                    'field_type' => 'multi_select',
                    'allow_null' => 1,
                    'add_term' => 0,
                    'save_terms' => 0,
                    'load_terms' => 0,
                    'return_format' => 'id',
                    'multiple' => 0,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-attach-to-calendar',
                    'label' => __('Attach to calendar', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-attach_to_calendar',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-exclude-from-calendar',
                    'label' => __('exclude from events', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-exclude_from_calendar',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_GREY_BOX-attach-to-calendar',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'sb_events',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-attach-to-members',
                    'label' => __('Attach to members', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-attach_to_members',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-exclude-from-members',
                    'label' => __('exclude from members', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-exclude_from_members',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_GREY_BOX-attach-to-members',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'medlemmar',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-attach-to-members_posts',
                    'label' => __('Attach to members posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-attach_to_members_posts',
                    'type' => 'true_false',
                    'ui' => 1,
                ],
                [
                    'key' => 'field_GLOBAL_GREY_BOX-exclude-from-members_posts',
                    'label' => __('exclude from members posts', self::TEXTDOMAIN),
                    'name' => 'GLOBAL_GREY_BOX-exclude_from_members_posts',
                    'type' => 'post_object',
                    'conditional_logic' => [
                        [
                            [
                                'field' => 'field_GLOBAL_GREY_BOX-attach-to-members_posts',
                                'operator' => '==',
                                'value' => '1',
                            ],
                        ],
                    ],
                    'post_type' => [
                        0 => 'sb_member_posts',
                    ],
                    'taxonomy' => '',
                    'allow_null' => 1,
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                ],
            ],
            'location' => [
                [
                    [
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'swedenbio-theme-options',
                    ],
                ],
            ],
            'menu_order' => 4,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
        ]);

    }
}
