<?php


namespace SwedenBio\Theme;


class SwedenBioOffers
{
    const CPT = "swedenbio_offers";
    const TEXTDOMAIN = "swedenbio";

    public function __construct()
    {
        add_action('init', [$this, 'addCPTandTaxonomy']);
        //add_filter('sb_article-cta-location', [$this, 'addToACFLocation']);
        add_filter('sb_intro-location', [$this, 'addToACFLocation']);
        add_filter('sb_article-cta-posttypes', [$this, 'addToACFPostObject']);
        add_filter('sb_header-buttons-posttypes', [$this, 'addToACFPostObject']);

        if (class_exists('ACF') && function_exists('acf_register_block_type')) {
            add_action('acf/init', [$this, 'register_block_offers']);
            add_action('acf/init', [$this, 'add_block_fields_offers']);
        }
    }

    public function addToACFLocation($array)
    {
        $array[][] = [
            'param' => 'post_type',
            'operator' => '==',
            'value' => self::CPT,
        ];

        return $array;
    }

    public function addToACFPostObject($array)
    {
        $array[] = self::CPT;

        return $array;
    }

    public function addCPTandTaxonomy()
    {
        register_post_type(
            self::CPT,
            [
                'labels' => [
                    'name'          => __('Member benefits', self::TEXTDOMAIN),
                    'singular_name' => __('Member benefits', self::TEXTDOMAIN),
                ],
                'public'      => true,
                'has_archive' => false,
                'rewrite'     => ['slug' => 'medlemsforman'],
                'show_in_rest' => true,
                'supports' => ['title', 'editor', 'comments', 'revisions', 'trackbacks',  'excerpt', 'page-attributes', 'thumbnail', 'custom-fields', 'author'],
                'menu_position' => 6,
                'menu_icon' => 'dashicons-businessman',
            ]
        );
    }

    public function register_block_offers() {

        acf_register_block_type([
            'name'              => 'vm-block-sb-offers',
            'title'             => __('SwedenBio Offers - SwedenBio', self::TEXTDOMAIN),
            'description'       => __('SwedenBio Offers for SwedenBio', self::TEXTDOMAIN),
            'example'           => [
                'attributes' => [
                    'mode' => 'preview',
                    'data' => [
                        'is_preview' => true,
                    ],
                ],
            ],
            'render_template'   => 'templates/blocks/sb-offers/sb-offers.php',
            'enqueue_style'     => get_template_directory_uri() . '/templates/blocks/sb-offers/sb-offers.css',
            'category'          => 'widgets',
            'icon'              => 'editor-ul',
            'keywords'          => ['swedenbio', 'offers'],
            'supports'          => [
                'align' => false,
            ],
        ]);

    }

    public function add_block_fields_offers() {

        acf_add_local_field_group(
            [
                'key' => 'group_vm_sb-offers',
                'title' => __('SwedenBio Offers', self::TEXTDOMAIN),
                'fields' =>
                    [
                        [
                            'key' => 'field_vm_sb-offers_title',
                            'label' => __('Title', self::TEXTDOMAIN),
                            'name' => 'vm_sb-offers_title',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_sb-offers_buttontext',
                            'label' => __('Button text', self::TEXTDOMAIN),
                            'name' => 'vm_sb-offers_buttontext',
                            'type' => 'text',
                        ],
                        [
                            'key' => 'field_vm_sb-offers_number',
                            'label' => __('Number of posts', self::TEXTDOMAIN),
                            'name' => 'vm_sb-offers_number',
                            'type' => 'number',
                        ],
                    ],
                'location' =>
                    [
                        [
                            [
                                'param' => 'block',
                                'operator' => '==',
                                'value' => 'acf/vm-block-sb-offers',
                            ],
                        ],
                    ],
            ]
        );
    }
}