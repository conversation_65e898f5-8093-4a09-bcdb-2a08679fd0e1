<?php
namespace SwedenBio\Theme;

class GlobalBlocks
{

    const TEXTDOMAIN = 'swedenbio';

    public function __construct()
    {
        add_shortcode('sb_global_red_box', [$this, 'redBox']);
        add_shortcode('sb_global_calendar_box', [$this, 'calendarBox']);
        add_shortcode('sb_global_gray_box', [$this, 'grayBox']);
    }

    public function redBox()
    {
        if (!class_exists('ACF')) {
            return '';
        }
        $title = get_field('GLOBAL_RED_BOX-title', "option");
        $CTA_text = get_field('GLOBAL_RED_BOX-cta_button_text', "option");
        $CTA_page = get_field('GLOBAL_RED_BOX-cta_button_url', "option");

        $content = "<div class='sb_red_box block-with-padding alignfull sb_block'>";
            $content .= "<div class='sb_red_box__content sb_block__content'>";
                $content .= "<h2 class='sb_red_box__content--title sb_block__content--title'>$title</h2>";
                $content .= "<div class='sb_red_box__content--posts sb_block__content--posts'>";

                    if (have_rows('GLOBAL_RED_BOX-content', "option")) {
                        while(have_rows('GLOBAL_RED_BOX-content', "option")) {
                            the_row();
                            $image = get_sub_field('GLOBAL_RED_BOX-image');
                            $text = get_sub_field('GLOBAL_RED_BOX-text');
                            $content .= "<div class='sb_red_box__content--posts__post sb_block__content--posts__post'>";
                                $content .= "<div class='box-post-thumbnail'>";
                                if ($image) {
                                    $content .= wp_get_attachment_image($image, 'news_list');
                                }
                                $content .= "</div>";
                                $content .= "<div class='box-post-text'>";
                                    $content .= $text;
                                $content .= "</div>";
                            $content .= "</div>";
                        }
                    }
                $content .= "</div>";
                    if ($CTA_page && $CTA_text) {
                        $content .= "<div class='sb_red_box__content--button-wrapper sb_block__content--button-wrapper'>";
                            $url = get_permalink($CTA_page);
                            $content .= "<a href='$url' title='$CTA_text' class='btn transparent-red'>$CTA_text</a>";
                        $content .= "</div>";
                    }
            $content .= "</div>";
        $content .= "</div>";

        return $content;
    }

    public function calendarBox()
    {
        if (!class_exists('ACF')) {
            return '';
        }
        $title = get_field('GLOBAL_CALENDAR_BOX-title', "option");
        $CTA_text = get_field('GLOBAL_CALENDAR_BOX-cta_button_text', "option");
        $args = [
            'post_type' => 'page',
            'fields' => 'ids',
            'nopaging' => true,
            'meta_key' => '_wp_page_template',
            'meta_value' => 'templates/swedenbio-events.php'
        ];
        $calendarTemplates = get_posts( $args );

        $content = "<div class='sb_calendar_box block-with-padding alignfull sb_block'>";
        $content .= "<div class='sb_calendar_box__content sb_block__content'>";
        $content .= "<h2 class='sb_calendar_box__content--title sb_block__content--title has-text-color'>$title</h2>";
        $content .= "<div class='sb_calendar_box__content--posts sb_block__content--posts'>";
        $calendarQuery =  new \WP_Query([
            'post_type' => 'sb_events',
            'post_status' => 'publish',
            'posts_per_page' => 3,
            'meta_key'   => 'sb-event_start_date',
            'orderby'    => 'meta_value_num',
            'order'      => 'ASC',
            'meta_query' => [
                'relation' => 'AND',
                [
                    'relation' => 'OR',
                    'start_date' => [
                        'key' => 'sb-event_start_date',
                        'value' => date('Ymd'),
                        'compare' => '>='
                    ],

                    'end_date' => [
                        'key' => 'sb-event_end_date',
                        'value' => date('Ymd'),
                        'compare' => '>='
                    ],

                ],
                [
                    'start_time' => [
                        'key' => 'sb-event_start_time',
                        'compare' => 'EXISTS',
                    ],
                ]
            ],
//            'orderby' => [
//                'start_date' => 'ASC',
//                'start_time' => 'ASC',
//            ],
        ]);

        if ($calendarQuery->have_posts()) {
            while ($calendarQuery->have_posts()) {
                $calendarQuery->the_post();
                $postId = get_the_ID();
                $startDate = get_field("sb-event_start_date", $postId);
                $endDate = get_field("sb-event_end_date", $postId);
                $place = get_field("sb-event_place", $postId);
                $type = get_field("sb-event_event_type", $postId);

                if (!$startDate) {
                    continue;
                }
                $now = new \DateTime();
                $sDate = new \DateTime($startDate);

                $content .= "<div class='sb_calendar_box__content--posts__post sb_block__content--posts__post'>";
                    $content .= "<div class='sb_calendar_box__content--posts__post--date has-text-color'>";
                        if ($endDate) {
                            $eDate = new \DateTime($endDate);
                            if ($sDate != $eDate) {
                                $content .= "<span class='event-date has-text-color'>";
                                    $content .= $sDate->format('j');
                                    if ($eDate->format('Y') == $sDate->format('Y')) {
                                        if (($sDate->format('n') == $eDate->format('n')) && ($now->format('Y') == $sDate->format('Y'))) {
                                            $content .= ' - '.$eDate->format('j/n');
                                        }

                                        if (($now->format('Y') == $sDate->format('Y')) && ($sDate->format('n') != $eDate->format('n'))) {
                                            $content .= $sDate->format('/n').' - '.$eDate->format('j/n');
                                        }

                                        if (($now->format('Y') != $sDate->format('Y')) && ($sDate->format('n') != $eDate->format('n'))) {
                                            $content .= $sDate->format('/n').' - '.$eDate->format('j/n Y');
                                        }

                                        if (($now->format('Y') != $sDate->format('Y')) && ($sDate->format('n') == $eDate->format('n'))) {
                                            $content .= ' - '.$eDate->format('j/n Y');
                                        }
                                    } else {
                                        $content .= $sDate->format('/n Y').' - '.$eDate->format('j/n Y');
                                    }
                                $content .= "</span>";
                            } else {
                                if ($now->format('Y') == $sDate->format('Y')) {
                                    $content .= "<span class='event-date has-text-color'>".$sDate->format('j/n')."</span>";
                                } else {
                                    $content .= "<span class='event-date has-text-color'>".$sDate->format('j/n Y')."</span>";
                                }
                            }
                        } else {
                            if ($now->format('Y') == $sDate->format('Y')) {
                                $content .= "<span class='event-date has-text-color'>".$sDate->format('j/n')."</span>";
                            } else {
                                $content .= "<span class='event-date has-text-color'>".$sDate->format('j/n Y')."</span>";
                            }
                        }
                    $content .= "</div>";

                    $content .= "<h2 class='event-title has-text-color'><a href='".get_permalink()."'>".get_the_title()."</a></h2>";
                    if ($place) {
                        $content .= "<span class='event-place has-text-color'>$place</span>";
                    }
                    $content .= "<span class='event-type has-text-color ".($type ==='partner' ? 'partner':'own')."'>";
                        if ($type === "own") {
                            $content .= __('SwedenBio event', 'swedenbio');
                        } else if ($type === 'partner') {
                            $content .= __('Partner event', 'swedenbio');
                        } else if ($type === 'external') {
                            $content .= __('External event', 'swedenbio');
                        }
                    $content .= "</span>";
                $content .= "</div>";
            }
        }
        wp_reset_postdata();
        $content .= "</div>";
        if ($CTA_text && isset($calendarTemplates[0])) {
            $content .= "<div class='sb_calendar_box__content--button-wrapper sb_block__content--button-wrapper'>";
            $url = get_permalink($calendarTemplates[0]);
            $content .= "<a href='$url' title='$CTA_text' class='btn transparent-blue'>$CTA_text</a>";
            $content .= "</div>";
        }
        $content .= "</div>";
        $content .= "</div>";

        return $content;
    }

    public function grayBox()
    {
        if (!class_exists('ACF')) {
            return '';
        }
        $title = get_field('GLOBAL_GREY_BOX-title', "option");
        $CTA_text = get_field('GLOBAL_GREY_BOX-cta_button_text', "option");
        $CTA_page = get_field('GLOBAL_GREY_BOX-cta_button_url', "option");

        $content = "<div class='sb_gray_box block-with-padding alignfull sb_block'>";
        $content .= "<div class='sb_block__content'>";
        $content .= "<h2 class='sb_block__content--title'>$title</h2>";
        $content .= "<div class='sb_block__content--posts'>";

        if (have_rows('GLOBAL_GREY_BOX-content', "option")) {
            while(have_rows('GLOBAL_GREY_BOX-content', "option")) {
                the_row();
                $image = get_sub_field('GLOBAL_GREY_BOX-image');
                $text = get_sub_field('GLOBAL_GREY_BOX-text');
                $content .= "<div class='sb_block__content--posts__post'>";
                $content .= "<div class='box-post-thumbnail'>";
                if ($image) {
                    $content .= wp_get_attachment_image($image, 'news_list');
                }
                $content .= "</div>";
                $content .= "<div class='box-post-text'>";
                $content .= $text;
                $content .= "</div>";
                $content .= "</div>";
            }
        }
        $content .= "</div>";
        if ($CTA_page && $CTA_text) {
            $content .= "<div class='sb_block__content--button-wrapper'>";
            $url = get_permalink($CTA_page);
            $content .= "<a href='$url' title='$CTA_text' class='btn'>$CTA_text</a>";
            $content .= "</div>";
        }
        $content .= "</div>";
        $content .= "</div>";

        return $content;
    }
}
