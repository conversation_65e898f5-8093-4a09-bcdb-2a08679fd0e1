<?php


namespace SwedenBio\Theme;


class FixPosts
{
    public function __construct()
    {
        add_action( 'admin_menu', [$this, 'register_sub_menu']);
        add_action( 'admin_init', [$this, 'fix']);
    }

    public function register_sub_menu() {
        add_submenu_page(
            'options-general.php', 'Fix posts', 'Fix posts', 'manage_options', 'sb-fix_posts', [$this, 'submenu_page_callback']
        );
    }

    public function fix() {
        if (isset($_POST['sb_fix_posts']) && $_POST['sb_fix_posts']) {
            $args = [
                'post_type' => 'sb_events',
                'post_status' => 'publish',
                'posts_per_page' => -1
            ];
            $query = new \WP_Query($args);
            if ($query->have_posts()) {
                while ($query->have_posts()) {
                    $query->the_post();
                    $postId = get_the_ID();
                    $content = get_the_content($postId);
                    $new_content = get_post_meta($postId, 'text', true);
                    //$nc = str_replace('http://swedenbio.se/', 'http://swedenbio.hemsida.eu/', $new_content);
                    //$nc = str_replace('https://swedenbio.se/', 'http://swedenbio.hemsida.eu/', $nc);
                    update_post_meta($postId, 'sb_intro', $content);
                    $my_post = [
                        'ID' =>  $postId,
                        'post_content' => $new_content,
                    ];
                    wp_update_post( $my_post );
                }
            }
        }
    }

    public function submenu_page_callback() {
        echo '<div class="wrap">';
            echo '<h2>Fix posts</h2>';
            echo "<form action='' method='post'>";
                echo "<input name='sb_fix_posts' type='submit'>";
            echo "</form>";
        echo '</div>';
    }
}