<?php get_header();
$pageForPosts = get_option('page_for_posts');
?>

<main role="main" id="content" class="content clearfix">
<?php while (have_posts()): the_post(); ?>

<?php get_template_part('page-header'); ?>
<section class="article-content single">
    <div class="single-article-wrapper">
        <aside class="article-aside">
            <?php
            if (is_singular(['sb_events'])) {
                get_template_part('templates/partials/column-content-event');
            } else {
                get_template_part('templates/partials/column-content-post');
            }
            ?>
        </aside>
        <article <?php post_class('article'); ?>>
            <?php the_content(''); ?>
        </article>
    </div>
    <div class="cta-container">
        <?php
        get_template_part('templates/partials/after-article-cta-buttons');
        if (is_singular(['sb_events'])) {
            $args = [
                'post_type' => 'page',
                'fields' => 'ids',
                'nopaging' => true,
                'meta_key' => '_wp_page_template',
                'meta_value' => 'templates/swedenbio-events.php'
            ];
            $calendarTemplates = get_posts( $args );
            if ($calendarTemplates && isset($calendarTemplates[0])) {
            ?>
            <a href="<?php echo get_permalink($calendarTemplates[0]); ?>" class="btn small white"><?php _e("Back to calendar", 'swedenbio'); ?></a>
       <?php
            }
            } else { ?>
            <a href="<?php echo get_permalink($pageForPosts); ?>" class="btn small white"><?php _e("Back to news", 'swedenbio'); ?></a>
        <?php }
        ?>

    </div>
</section>
<?php endwhile; ?>
</main>

<?php get_footer();