    </div>
    <?php
    get_template_part('templates/partials/footer-attachements');
    ?>
    <div class="newsletter-popup-container">
        <a href="#" class="close" aria-label="<?php _e('Close', 'swedenbio'); ?>"><i class="far fa-times-circle"></i></a>
        <div class="newsletter-popup-container__content">
            <?php
            echo do_shortcode('[sb_newsletter title="' . __('Subscribe to our newsletter', 'swedenbio') . '"]');
            $newsletter_description = (class_exists('ACF') ? get_field('newsletter_description', 'options') : false);
            if ($newsletter_description) {
                echo "<div class='description'>".$newsletter_description."</div>";
            }
            ?>
        </div>
    </div>
    <footer class="footer">
        <div class="footer-content">
            <div class="columns">
                <div class="footer-content__contact">
                    <h2 class="footer-title"><?php _e('Contact', 'swedenbio'); ?></h2>
                    <div>
                        <?php dynamic_sidebar('footer-column-1'); ?>
                    </div>
                    <?php echo do_shortcode('[sb_newsletter title="' . __('Subscribe to our newsletter', 'swedenbio') . '"]'); ?>
                </div>
                <div class="footer-content__information">
                    <h2 class="footer-title"><?php _e('Information', 'swedenbio'); ?></h2>
                    <div class="columns">
                        <div class="column">
                            <?php dynamic_sidebar('footer-column-2'); ?>
                        </div>
                        <div class="column">
                            <?php dynamic_sidebar('footer-column-3'); ?>
                        </div>
                        <div class="column">
                            <?php dynamic_sidebar('footer-column-4'); ?>
                        </div>
                        <div class="column">
                            <?php dynamic_sidebar('footer-column-5'); ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="copyright">
                &copy; <?php echo date("Y"); ?> SwedenBIO
            </div>
        </div>
    </footer>
    <?php wp_footer(); ?>
</body>
</html>