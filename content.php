<article <?php post_class('loop-article'); ?>>
    <?php
        $postId = get_the_ID();
        $image = get_the_post_thumbnail($postId, 'news_list');
        if ($image) {
            echo "<div class='loop-article__image'>";
                echo $image;
            echo "</div>";
        }
    ?>
    <div class="loop-article__content">
        <span class="loop-article__content--date">
            <?php
                echo get_the_date();
            ?></span>
        <h3 class="loop-article__content--title">
            <a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>">
                <?php the_title(); ?>
            </a>
        </h3>
        <?php do_action('get_intro', $postId);
        $terms = get_the_terms($postId, 'category');
        echo "<div class='loop-article__content--categories'>";
            if ($terms) {
                foreach ($terms as $term) {
                    $link = get_term_link($term);
                    if ($term->term_id !== 1) {
                        echo "<span class='category'>";
                            if ($link) {
                                echo "<a href='$link'>";
                            }
                            echo $term->name;
                            if ($link) {
                                echo "</a>";
                            }
                        echo "</span>";
                    }
                }
            }
        echo "</div>";
        ?>
    </div>
</article>