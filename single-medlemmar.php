<?php get_header();
?>

<main role="main" id="content" class="content clearfix">
<?php while (have_posts()): the_post(); ?>

<?php get_template_part('page-header'); ?>
<section class="article-content single no-column">
    <div class="single-article-wrapper">
        <article <?php post_class('article'); ?>>
            <?php the_content(''); ?>
        </article>
    </div>
    <?php
    $args = [
        'post_type' => 'page',
        'fields' => 'ids',
        'nopaging' => true,
        'meta_key' => '_wp_page_template',
        'meta_value' => 'templates/swedenbio-members.php'
    ];
    $templates = get_posts( $args );
    if ($templates && isset($templates[0])) {
        $terms = get_the_terms(get_the_ID(), "user_categories");
        if ($terms) {
            echo "<div class='cta-container categories'>";
            foreach ($terms as $term) {
                echo "<a href='".get_permalink($templates[0])."#".$term->slug."' class='btn small transparent-gray'>".$term->name."</a>";
            }
            echo "</div>";
        }
    }
    ?>
    <div class="cta-container">
        <?php
            if (class_exists("ACF")) {
                $website = get_field('website');
                if ($website) {
                    echo "<a href='$website' class='btn red'>".__("Website", 'swedenbio')."</a>";
                }
                $biotech = get_field('biotechgate_link');
                if ($biotech) {
                    echo "<a href='$biotech' class='btn red'>".__("Biotech gate", 'swedenbio')."</a>";
                }
            }

            if ($templates && isset($templates[0])) {
            ?>
            <a href="<?php echo get_permalink($templates[0]); ?>" class="btn blue-outline"><?php _e("Back to members", 'swedenbio'); ?></a>
       <?php
            }

        ?>

    </div>
</section>
<?php endwhile; ?>
</main>

<?php get_footer();