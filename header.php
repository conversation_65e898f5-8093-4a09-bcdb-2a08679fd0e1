<!DOCTYPE html>
<html class="<?php language_attributes(); ?>>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php do_action('swedenbio_browser-title'); ?></title>
    <?php
        if (class_exists('ACF')) {
            $favicon = get_field('sb_favicon', "options");
            if ($favicon) {
                echo '<link rel="icon" type="image/png" href="'.$favicon.'">';
            }
        }
    ?>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <?php wp_head(); ?>
    <meta name="format-detection" content="telephone=no">
    <link rel="alternate" type="application/rss+xml" title="<?php echo get_bloginfo('name').' '.__('Feed', 'swedenbio'); ?>" href="<?php echo esc_url(get_feed_link()); ?>">
</head>
<body <?php body_class(); ?>>
    <header class="main-header">
        <div class="main-header__background"></div>
        <div class="main-header__wrapper ">
            <div class="main-header__wrapper--menu-row">
                <div class="logo">
                    <a href="<?php echo home_url(); ?>" class="logo-wrapper"><?php
                        if (!class_exists("acf")) {
                            echo get_bloginfo('name');
                        } else {
                            $logo = get_field('header_logo', "option");
                            if ($logo) {
                                echo "<img src='".$logo."' alt='".__("Logo", 'swedenbio')."' />";
                            } else {
                                echo get_bloginfo('name');
                            }
                        }
                        ?></a>
                </div>

                <div class="main-menu">
                    <ul class="main-menu__items">
                        <?php
                        $menu = wp_nav_menu([
                                'theme_location' => 'main-menu',
                                'container'=> false,
                                'fallback_cb' => false,
                                'depth' => 3,
                                "echo" => false
                            ]
                        );
                        echo preg_replace([
                            '#^<ul[^>]*>#',
                            '#</ul>$#'
                        ], '', $menu);
                        ?>
                        <li class="menu-item-with-search">
                            <a href="#" class="search-button" aria-label="<?php _e("Open search", 'swedenbio'); ?>">
                                <img src="<?php echo get_stylesheet_directory_uri().'/assets/images/search.svg' ?>" alt="<?php _e("Open search", 'swedenbio'); ?>" />
                            </a>
                        </li>
                        <?php if (class_exists("acf") && get_field('en_page', 'option')) { ?>
                            <li class="language-switch">
                                <a href="<?php echo ((class_exists("acf") && get_field('en_page', 'option')) ? get_permalink(get_field('en_page', 'option')) : "#") ?>" class="language-switch-link" aria-label="<?php _e("Language switch", 'swedenbio'); ?>">
                                    <img src="<?php echo get_stylesheet_directory_uri().'/assets/images/language.svg' ?>" alt="<?php _e("language switch", 'swedenbio'); ?>" />
                                </a>
                            </li>
                        <?php } ?>
                        <li class="hamburger-menu">
                            <a href="#" class="hamburger-menu-button" aria-label="<?php _e("Hamburger menu", 'swedenbio'); ?>">
                                <img src="<?php echo get_stylesheet_directory_uri().'/assets/images/menu.svg' ?>" alt="<?php _e("Hamburger menu", 'swedenbio'); ?>" />
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="main-header__buttons <?php if (is_front_page()) {echo "homepage";} ?>">
                <?php
                if (class_exists("acf") && function_exists("have_rows")) {

                    if (have_rows('header_buttons_repeater', "option")) {
                        echo "<div class='main-header__buttons--wrapper'>";
                        while(have_rows('header_buttons_repeater', "option")) {
                            the_row();
                            $textShort = get_sub_field('text_short');
                            $textLong = get_sub_field('text_long');
                            $icon = get_sub_field('icon');
                            $url = get_sub_field('url');
                            $onlyMobile = get_sub_field('only_mobile');
                            $currentPage = get_the_ID();
                            if ($textLong && $textShort) {
                                echo "<div class='main-header__buttons--wrapper__button ".($onlyMobile ? "only-mobile" : "")."'>";
                                    echo "<a class='sb-header-button ".(($url === $currentPage && is_singular()) ? 'active' : '')."' href='".($url ? get_permalink($url) : "#")."'>";
                                        echo "<div class='sb-header-button__icon'>";
                                            if ($icon) {
                                                echo "<img src='$icon' alt='".__("Icon", 'swedenbio')."'>";
                                            }
                                        echo "</div>";
                                        echo "<div class='sb-header-button__text'>";
                                            echo "<span class='sb-header-button__text--short-text'>$textShort</span>";
                                            echo "<span class='sb-header-button__text--long-text'>$textLong</span>";
                                        echo "</div>";
                                        echo "<div class='sb-header-button__arrow'>";
                                            echo "<img src='".get_stylesheet_directory_uri()."/assets/images/SWEBIO_Icon_Arrow.png' alt='".__('Go to', 'swedenbio')."'>";
                                        echo "</div>";
                                    echo "</a>";
                                echo "</div>";
                            }
                        }
                        echo "</div>";
                    }
                }
                ?>
            </div>
            <?php do_action('sb_header_content'); ?>
        </div>

        <div class="secondary-menu">
            <div class="secondary-menu__wrapper">
                <div class="search-row">
                    <?php get_template_part('searchform');  ?>
                    <a href="#" class="secondary-menu-close" aria-label="<?php _e("Menu close", 'swedenbio'); ?>"><i class="far fa-times-circle"></i></a>
                </div>

                <?php
                wp_nav_menu([
                        'theme_location' => 'secondary-menu',
                        'menu_class' => 'secondary-menu__items clearfix',
                        'container'=> false,
                        'fallback_cb' => false,
                        'depth' => 3
                    ]
                );
                ?>
            </div>
        </div>

    </header>

    <div id="main-wrapper" class="main-wrapper">