<?php
/***
 * Autoload requested files
 */
spl_autoload_register('theme_autoload_classes');
function theme_autoload_classes($class_name)
{
    $class_file = str_replace('SwedenBio\\', '', $class_name);
    $class_file = str_replace(['_', '\\'], DIRECTORY_SEPARATOR, $class_file);
    $path = get_template_directory() . DIRECTORY_SEPARATOR . 'src' . DIRECTORY_SEPARATOR;
    $fullpath = $path . $class_file  . '.php';
    if (file_exists($fullpath)) {
        require_once $fullpath;
    }
}

// Initialize theme
new \SwedenBio\Theme\ThemeSetup();
new \SwedenBio\Theme\ACF();
new \SwedenBio\Theme\GlobalBlocks();
new \SwedenBio\Theme\GutenbergBlocks();
new \SwedenBio\Theme\Events();
new \SwedenBio\Theme\Members();
new \SwedenBio\Theme\MembersPosts();
new \SwedenBio\Theme\SwedenBioOffers();
new \SwedenBio\Theme\MembersOffers();
//new \SwedenBio\Theme\FixPosts();

function comment_html($comment, $args, $depth) {
    $GLOBALS['comment'] = $comment; ?>
<li <?php comment_class(); ?> id="li-comment-<?php comment_ID() ?>">
    <div id="comment-<?php comment_ID(); ?>" class="sb-comment">
        <div class="sb-comment__author">
                <span class="name">
                    <?php
                        echo $comment->comment_author;
                    ?>
                </span>
                <a href="<?php echo htmlspecialchars( get_comment_link( $comment->comment_ID ) ) ?>">
                    <?php printf(__('%1$s %2$s'), get_comment_date("d M Y"),  get_comment_time("H:i")) ?>
                </a>
                <?php edit_comment_link(__('(Edit)'),'  ','') ?>
        </div>
        <div class="sb-comment__content">

            <?php if ($comment->comment_approved == '0') : ?>
                <em><?php _e('Your comment is awaiting moderation.') ?></em>
                <br />
            <?php endif; ?>
            <?php comment_text() ?>

            <div>
                <?php comment_reply_link(array_merge( $args, array('depth' => $depth, 'max_depth' => $args['max_depth']))) ?>
            </div>
        </div>


    </div>
    <?php
}

// Code from Niran - Add 10 minute update interval to RSS feed fetching - maybe it work for RSS blocks
add_filter('wp_feed_cache_transient_lifetime', function() {
    return 600;
});

// Limit media library access for logged in users. Eg. Registered Users in Anslagstavlan
add_filter( 'ajax_query_attachments_args', 'wpb_show_current_user_attachments' );
 
function wpb_show_current_user_attachments( $query ) {
    $user_id = get_current_user_id();
    if ( $user_id && !current_user_can('activate_plugins') && !current_user_can('edit_others_posts') ) {
        $query['author'] = $user_id;
    }
    return $query;
} 

/**
 * Disable auto sizes for images
 * https://core.trac.wordpress.org/ticket/61847#comment:23
 */
add_filter('wp_content_img_tag', static function($image) {
    return str_replace(' sizes="auto, ', ' sizes="', $image);
});

add_filter('wp_get_attachment_image_attributes', static function($attr) {
    if (isset($attr['sizes'])) {
        $attr['sizes'] = preg_replace('/^auto, /', '', $attr['sizes']);
    }
    return $attr;
});