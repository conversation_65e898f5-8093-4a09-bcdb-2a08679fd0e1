on: 
  push:
    branches:
      - master
name: 🚀 Deploy website to PROD on push
jobs:
  web-deploy:
    name: 🎉 Deploy to PROD
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v3

    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.4
      with:
        server: ftp.swedenbio.se
        username: <EMAIL>
        password: ${{ secrets.ftp_password }}
        server-dir: /public_html/wp-content/themes/SwedenBio/