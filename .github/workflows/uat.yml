on: 
  push:
    branches:
      - uat
name: 🚀 Deploy website to UAT on push
jobs:
  web-deploy:
    name: 🎉 Deploy to UAT
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v3

    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@v4.3.4
      with:
        server: ftp.swedenbio.se
        username: <EMAIL>
        password: ${{ secrets.ftp_password }}
        server-dir: /uat-wp.swedenbio.se/wp-content/themes/SwedenBio/